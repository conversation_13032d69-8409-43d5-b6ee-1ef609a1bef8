// 模拟数据生成器

import { 
  MedicalCase, 
  CaseType, 
  CaseStatus, 
  Priority,
  Patient,
  Department,
  Ward,
  Bed,
  BedStatus,
  BedType,
  WardType,
  DepartmentType,
  Gender,
  UserRole,
  User
} from '@/types';

// 生成随机ID
function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// 生成随机日期
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// 模拟患者数据
export const mockPatients: Patient[] = [
  {
    id: 'patient-1',
    patientId: 'P001',
    name: '张三',
    gender: Gender.MALE,
    dateOfBirth: new Date('1980-05-15'),
    age: 44,
    phone: '13800138001',
    email: '<EMAIL>',
    address: {
      street: '北京市朝阳区建国路1号',
      city: '北京',
      province: '北京',
      postalCode: '100000',
      country: '中国'
    },
    emergencyContact: {
      name: '李四',
      relationship: '配偶',
      phone: '13800138002'
    },
    medicalHistory: [],
    allergies: ['青霉素'],
    medications: [],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 'patient-2',
    patientId: 'P002',
    name: '王丽',
    gender: Gender.FEMALE,
    dateOfBirth: new Date('1975-08-20'),
    age: 49,
    phone: '13800138003',
    email: '<EMAIL>',
    address: {
      street: '上海市浦东新区陆家嘴1号',
      city: '上海',
      province: '上海',
      postalCode: '200000',
      country: '中国'
    },
    medicalHistory: [],
    allergies: [],
    medications: [],
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  }
];

// 模拟科室数据
export const mockDepartments: Department[] = [
  {
    id: 'dept-1',
    name: '内科',
    code: 'INT',
    type: DepartmentType.CLINICAL,
    location: '1号楼2层',
    capacity: 50,
    head: '李主任',
    contactPhone: '010-12345678',
    description: '内科诊疗',
    isActive: true,
    stats: {
      totalBeds: 50,
      occupiedBeds: 35,
      activeCases: 40,
      totalStaff: 25
    }
  },
  {
    id: 'dept-2',
    name: '外科',
    code: 'SUR',
    type: DepartmentType.CLINICAL,
    location: '2号楼3层',
    capacity: 40,
    head: '王主任',
    contactPhone: '010-12345679',
    description: '外科手术',
    isActive: true,
    stats: {
      totalBeds: 40,
      occupiedBeds: 28,
      activeCases: 32,
      totalStaff: 30
    }
  },
  {
    id: 'dept-3',
    name: '急诊科',
    code: 'ER',
    type: DepartmentType.CLINICAL,
    location: '1号楼1层',
    head: '赵主任',
    contactPhone: '010-12345680',
    description: '急诊医疗',
    isActive: true,
    stats: {
      activeCases: 15,
      totalStaff: 20
    }
  }
];

// 模拟病房数据
export const mockWards: Ward[] = [
  {
    id: 'ward-1',
    name: '内科一病房',
    departmentId: 'dept-1',
    department: mockDepartments[0],
    floor: 2,
    totalBeds: 25,
    availableBeds: 8,
    wardType: WardType.GENERAL,
    nurseStation: '2A护士站',
    isActive: true,
    beds: []
  },
  {
    id: 'ward-2',
    name: '内科二病房',
    departmentId: 'dept-1',
    department: mockDepartments[0],
    floor: 2,
    totalBeds: 25,
    availableBeds: 7,
    wardType: WardType.GENERAL,
    nurseStation: '2B护士站',
    isActive: true,
    beds: []
  }
];

// 模拟床位数据
export const mockBeds: Bed[] = [
  {
    id: 'bed-1',
    bedNumber: '201-1',
    wardId: 'ward-1',
    bedType: BedType.STANDARD,
    status: BedStatus.OCCUPIED,
    currentPatientId: 'patient-1',
    currentCaseId: 'case-1',
    lastCleanedAt: new Date('2024-01-15T08:00:00')
  },
  {
    id: 'bed-2',
    bedNumber: '201-2',
    wardId: 'ward-1',
    bedType: BedType.STANDARD,
    status: BedStatus.AVAILABLE,
    lastCleanedAt: new Date('2024-01-15T10:00:00')
  }
];

// 模拟医护人员数据
export const mockStaff: User[] = [
  {
    id: 'doctor-1',
    username: 'dr.zhang',
    email: '<EMAIL>',
    phone: '13800138010',
    role: UserRole.DOCTOR,
    status: 'active' as any,
    profile: {
      firstName: '张',
      lastName: '医生',
      displayName: '张医生',
      department: '内科',
      position: '主治医师'
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 'doctor-2',
    username: 'dr.wang',
    email: '<EMAIL>',
    phone: '13800138011',
    role: UserRole.DOCTOR,
    status: 'active' as any,
    profile: {
      firstName: '王',
      lastName: '医生',
      displayName: '王医生',
      department: '外科',
      position: '副主任医师'
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];

// 生成模拟病例数据
export function generateMockCases(count: number = 50): MedicalCase[] {
  const cases: MedicalCase[] = [];
  const caseTypes = Object.values(CaseType);
  const caseStatuses = Object.values(CaseStatus);
  const priorities = Object.values(Priority);
  
  for (let i = 0; i < count; i++) {
    const caseId = `CASE${String(i + 1).padStart(4, '0')}`;
    const patientIndex = i % mockPatients.length;
    const departmentIndex = i % mockDepartments.length;
    const doctorIndex = i % mockStaff.length;
    
    const admissionDate = randomDate(new Date('2024-01-01'), new Date());
    const isCompleted = Math.random() > 0.7;
    const dischargeDate = isCompleted ? randomDate(admissionDate, new Date()) : undefined;
    
    const medicalCase: MedicalCase = {
      id: `case-${i + 1}`,
      caseId,
      patientId: mockPatients[patientIndex].id,
      patient: mockPatients[patientIndex],
      caseType: caseTypes[Math.floor(Math.random() * caseTypes.length)],
      status: isCompleted ? CaseStatus.COMPLETED : caseStatuses[Math.floor(Math.random() * (caseStatuses.length - 1))],
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      
      admissionDate,
      dischargeDate,
      department: mockDepartments[departmentIndex].name,
      ward: Math.random() > 0.5 ? mockWards[0].name : undefined,
      bedNumber: Math.random() > 0.5 ? `${201 + i % 10}-${(i % 2) + 1}` : undefined,
      
      chiefComplaint: [
        '胸痛3天',
        '发热伴咳嗽1周',
        '腹痛2天',
        '头痛头晕',
        '呼吸困难',
        '恶心呕吐'
      ][i % 6],
      
      presentIllness: '患者于3天前无明显诱因出现胸痛，为持续性钝痛，无放射痛...',
      pastHistory: '既往体健，否认高血压、糖尿病等慢性疾病史',
      familyHistory: '父母健在，家族中无遗传性疾病史',
      personalHistory: '不吸烟，偶尔饮酒',
      
      primaryDiagnosis: [
        '急性心肌梗死',
        '肺炎',
        '急性阑尾炎',
        '脑梗死',
        '哮喘急性发作',
        '急性胃炎'
      ][i % 6],
      
      secondaryDiagnoses: [],
      differentialDiagnosis: '需排除其他心血管疾病',
      
      treatmentPlan: '1. 卧床休息\n2. 心电监护\n3. 抗凝治疗\n4. 对症支持治疗',
      medications: [],
      procedures: [],
      
      attendingPhysician: mockStaff[doctorIndex].profile.displayName,
      residentPhysician: Math.random() > 0.5 ? '李住院医师' : undefined,
      nurseInCharge: Math.random() > 0.5 ? '护士长' : undefined,
      
      totalCost: Math.floor(Math.random() * 50000) + 5000,
      insuranceCoverage: Math.floor(Math.random() * 30000) + 3000,
      
      medicalRecords: [],
      labResults: [],
      imagingResults: [],
      attachments: [],
      statusHistory: [],
      notes: [],
      
      createdAt: admissionDate,
      updatedAt: dischargeDate || new Date()
    };
    
    cases.push(medicalCase);
  }
  
  return cases;
}

// 导出模拟数据
export const mockCases = generateMockCases(50);

// 数据查找工具
export function findCaseById(id: string): MedicalCase | undefined {
  return mockCases.find(c => c.id === id);
}

export function findPatientById(id: string): Patient | undefined {
  return mockPatients.find(p => p.id === id);
}

export function findDepartmentById(id: string): Department | undefined {
  return mockDepartments.find(d => d.id === id);
}

// 分页工具
export function paginateData<T>(data: T[], page: number = 1, limit: number = 10) {
  const offset = (page - 1) * limit;
  const paginatedData = data.slice(offset, offset + limit);
  
  return {
    data: paginatedData,
    pagination: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
      hasNext: offset + limit < data.length,
      hasPrev: page > 1
    }
  };
}
