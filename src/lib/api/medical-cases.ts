// 医疗病例 API 接口

import {
    Bed,
    CaseExportOptions,
    CaseFormData,
    CaseImportResult,
    CaseMedication,
    CaseNote,
    CaseQueryParams,
    CaseStatistics,
    CaseUpdateData,
    Department,
    ImagingResult,
    LabResult,
    MedicalCase,
    MedicalProcedure,
    MedicalRecord,
    Ward
} from '@/types';
import {
    apiClient,
    handleApiResponse,
    handlePaginatedResponse,
    PaginatedResponse,
    withRetry
} from './client';

export class MedicalCaseApi {
  // 获取病例列表
  static async getCases(params?: CaseQueryParams): Promise<PaginatedResponse<MedicalCase>> {
    const response = await apiClient.get<PaginatedResponse<MedicalCase>>('/medical-cases', params);
    return handlePaginatedResponse(response);
  }

  // 获取单个病例详情
  static async getCase(caseId: string): Promise<MedicalCase> {
    const response = await apiClient.get<MedicalCase>(`/medical-cases/${caseId}`);
    return handleApiResponse(response);
  }

  // 创建新病例
  static async createCase(data: CaseFormData): Promise<MedicalCase> {
    const response = await apiClient.post<MedicalCase>('/medical-cases', data);
    return handleApiResponse(response);
  }

  // 更新病例
  static async updateCase(caseId: string, data: CaseUpdateData): Promise<MedicalCase> {
    const response = await apiClient.put<MedicalCase>(`/medical-cases/${caseId}`, data);
    return handleApiResponse(response);
  }

  // 删除病例
  static async deleteCase(caseId: string): Promise<void> {
    const response = await apiClient.delete(`/medical-cases/${caseId}`);
    handleApiResponse(response);
  }

  // 批量删除病例
  static async deleteCases(caseIds: string[]): Promise<void> {
    const response = await apiClient.post('/medical-cases/batch-delete', { caseIds });
    handleApiResponse(response);
  }

  // 更新病例状态
  static async updateCaseStatus(
    caseId: string, 
    status: string, 
    reason?: string
  ): Promise<MedicalCase> {
    const response = await apiClient.patch<MedicalCase>(`/medical-cases/${caseId}/status`, {
      status,
      reason
    });
    return handleApiResponse(response);
  }

  // 转科
  static async transferCase(
    caseId: string, 
    toDepartment: string, 
    reason: string
  ): Promise<MedicalCase> {
    const response = await apiClient.post<MedicalCase>(`/medical-cases/${caseId}/transfer`, {
      toDepartment,
      reason
    });
    return handleApiResponse(response);
  }

  // 出院
  static async dischargeCase(
    caseId: string, 
    dischargeData: {
      dischargeDate: Date;
      dischargeSummary: string;
      followUpInstructions?: string;
      medications?: CaseMedication[];
    }
  ): Promise<MedicalCase> {
    const response = await apiClient.post<MedicalCase>(`/medical-cases/${caseId}/discharge`, dischargeData);
    return handleApiResponse(response);
  }

  // 获取病例统计数据
  static async getCaseStatistics(params?: {
    dateRange?: { start: Date; end: Date };
    department?: string;
  }): Promise<CaseStatistics> {
    const response = await apiClient.get<CaseStatistics>('/medical-cases/statistics', params);
    return handleApiResponse(response);
  }

  // 搜索病例
  static async searchCases(query: string, filters?: Partial<CaseQueryParams>): Promise<MedicalCase[]> {
    const response = await apiClient.get<MedicalCase[]>('/medical-cases/search', {
      q: query,
      ...filters
    });
    return handleApiResponse(response);
  }

  // 导出病例数据
  static async exportCases(options: CaseExportOptions): Promise<Blob> {
    const response = await fetch('/api/medical-cases/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(options),
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }

  // 导入病例数据
  static async importCases(file: File): Promise<CaseImportResult> {
    const response = await apiClient.upload<CaseImportResult>('/medical-cases/import', file);
    return handleApiResponse(response);
  }

  // 复制病例
  static async duplicateCase(caseId: string, newPatientId?: string): Promise<MedicalCase> {
    const response = await apiClient.post<MedicalCase>(`/medical-cases/${caseId}/duplicate`, {
      newPatientId
    });
    return handleApiResponse(response);
  }
}

// 医疗记录 API
export class MedicalRecordApi {
  // 获取病例的医疗记录
  static async getRecords(caseId: string): Promise<MedicalRecord[]> {
    const response = await apiClient.get<MedicalRecord[]>(`/medical-cases/${caseId}/records`);
    return handleApiResponse(response);
  }

  // 添加医疗记录
  static async addRecord(caseId: string, record: Omit<MedicalRecord, 'id'>): Promise<MedicalRecord> {
    const response = await apiClient.post<MedicalRecord>(`/medical-cases/${caseId}/records`, record);
    return handleApiResponse(response);
  }

  // 更新医疗记录
  static async updateRecord(
    caseId: string, 
    recordId: string, 
    data: Partial<MedicalRecord>
  ): Promise<MedicalRecord> {
    const response = await apiClient.put<MedicalRecord>(
      `/medical-cases/${caseId}/records/${recordId}`, 
      data
    );
    return handleApiResponse(response);
  }

  // 删除医疗记录
  static async deleteRecord(caseId: string, recordId: string): Promise<void> {
    const response = await apiClient.delete(`/medical-cases/${caseId}/records/${recordId}`);
    handleApiResponse(response);
  }
}

// 检验结果 API
export class LabResultApi {
  // 获取病例的检验结果
  static async getResults(caseId: string): Promise<LabResult[]> {
    const response = await apiClient.get<LabResult[]>(`/medical-cases/${caseId}/lab-results`);
    return handleApiResponse(response);
  }

  // 添加检验结果
  static async addResult(caseId: string, result: Omit<LabResult, 'id'>): Promise<LabResult> {
    const response = await apiClient.post<LabResult>(`/medical-cases/${caseId}/lab-results`, result);
    return handleApiResponse(response);
  }

  // 更新检验结果
  static async updateResult(
    caseId: string, 
    resultId: string, 
    data: Partial<LabResult>
  ): Promise<LabResult> {
    const response = await apiClient.put<LabResult>(
      `/medical-cases/${caseId}/lab-results/${resultId}`, 
      data
    );
    return handleApiResponse(response);
  }

  // 删除检验结果
  static async deleteResult(caseId: string, resultId: string): Promise<void> {
    const response = await apiClient.delete(`/medical-cases/${caseId}/lab-results/${resultId}`);
    handleApiResponse(response);
  }
}

// 影像结果 API
export class ImagingResultApi {
  // 获取病例的影像结果
  static async getResults(caseId: string): Promise<ImagingResult[]> {
    const response = await apiClient.get<ImagingResult[]>(`/medical-cases/${caseId}/imaging-results`);
    return handleApiResponse(response);
  }

  // 添加影像结果
  static async addResult(caseId: string, result: Omit<ImagingResult, 'id'>): Promise<ImagingResult> {
    const response = await apiClient.post<ImagingResult>(`/medical-cases/${caseId}/imaging-results`, result);
    return handleApiResponse(response);
  }

  // 上传影像文件
  static async uploadImage(
    caseId: string, 
    resultId: string, 
    file: File
  ): Promise<{ url: string; thumbnailUrl?: string }> {
    const response = await apiClient.upload(
      `/medical-cases/${caseId}/imaging-results/${resultId}/images`, 
      file
    );
    return handleApiResponse(response);
  }
}

// 用药记录 API
export class MedicationApi {
  // 获取病例的用药记录
  static async getMedications(caseId: string): Promise<CaseMedication[]> {
    const response = await apiClient.get<CaseMedication[]>(`/medical-cases/${caseId}/medications`);
    return handleApiResponse(response);
  }

  // 添加用药记录
  static async addMedication(caseId: string, medication: Omit<CaseMedication, 'id'>): Promise<CaseMedication> {
    const response = await apiClient.post<CaseMedication>(`/medical-cases/${caseId}/medications`, medication);
    return handleApiResponse(response);
  }

  // 停用药物
  static async stopMedication(
    caseId: string, 
    medicationId: string, 
    endDate: Date, 
    reason?: string
  ): Promise<CaseMedication> {
    const response = await apiClient.patch<CaseMedication>(
      `/medical-cases/${caseId}/medications/${medicationId}/stop`, 
      { endDate, reason }
    );
    return handleApiResponse(response);
  }
}

// 医疗操作 API
export class ProcedureApi {
  // 获取病例的医疗操作
  static async getProcedures(caseId: string): Promise<MedicalProcedure[]> {
    const response = await apiClient.get<MedicalProcedure[]>(`/medical-cases/${caseId}/procedures`);
    return handleApiResponse(response);
  }

  // 添加医疗操作
  static async addProcedure(caseId: string, procedure: Omit<MedicalProcedure, 'id'>): Promise<MedicalProcedure> {
    const response = await apiClient.post<MedicalProcedure>(`/medical-cases/${caseId}/procedures`, procedure);
    return handleApiResponse(response);
  }
}

// 病例备注 API
export class CaseNoteApi {
  // 获取病例备注
  static async getNotes(caseId: string): Promise<CaseNote[]> {
    const response = await apiClient.get<CaseNote[]>(`/medical-cases/${caseId}/notes`);
    return handleApiResponse(response);
  }

  // 添加备注
  static async addNote(caseId: string, note: Omit<CaseNote, 'id' | 'createdAt'>): Promise<CaseNote> {
    const response = await apiClient.post<CaseNote>(`/medical-cases/${caseId}/notes`, note);
    return handleApiResponse(response);
  }

  // 删除备注
  static async deleteNote(caseId: string, noteId: string): Promise<void> {
    const response = await apiClient.delete(`/medical-cases/${caseId}/notes/${noteId}`);
    handleApiResponse(response);
  }
}

// 科室管理 API
export class DepartmentApi {
  // 获取所有科室
  static async getDepartments(): Promise<Department[]> {
    const response = await withRetry(() => 
      apiClient.get<Department[]>('/departments')
    );
    return handleApiResponse(response);
  }

  // 获取科室详情
  static async getDepartment(departmentId: string): Promise<Department> {
    const response = await apiClient.get<Department>(`/departments/${departmentId}`);
    return handleApiResponse(response);
  }
}

// 病房管理 API
export class WardApi {
  // 获取科室的病房
  static async getWards(departmentId?: string): Promise<Ward[]> {
    const params = departmentId ? { departmentId } : undefined;
    const response = await apiClient.get<Ward[]>('/wards', params);
    return handleApiResponse(response);
  }

  // 获取可用床位
  static async getAvailableBeds(departmentId?: string): Promise<Bed[]> {
    const params = departmentId ? { departmentId } : undefined;
    const response = await apiClient.get<Bed[]>('/beds/available', params);
    return handleApiResponse(response);
  }

  // 分配床位
  static async assignBed(bedId: string, caseId: string): Promise<Bed> {
    const response = await apiClient.post<Bed>(`/beds/${bedId}/assign`, { caseId });
    return handleApiResponse(response);
  }

  // 释放床位
  static async releaseBed(bedId: string): Promise<Bed> {
    const response = await apiClient.post<Bed>(`/beds/${bedId}/release`);
    return handleApiResponse(response);
  }
}
