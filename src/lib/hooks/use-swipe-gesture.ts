"use client";

import { useRef, useEffect, useCallback } from 'react';

interface SwipeGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
  preventDefaultTouchmove?: boolean;
}

export function useSwipeGesture(options: SwipeGestureOptions) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    preventDefaultTouchmove = false
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (preventDefaultTouchmove) {
      e.preventDefault();
    }
  }, [preventDefaultTouchmove]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!touchStartRef.current) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;

    // 检查是否是快速滑动（小于500ms）
    if (deltaTime > 500) {
      touchStartRef.current = null;
      return;
    }

    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // 确保滑动距离超过阈值
    if (Math.max(absDeltaX, absDeltaY) < threshold) {
      touchStartRef.current = null;
      return;
    }

    // 判断滑动方向
    if (absDeltaX > absDeltaY) {
      // 水平滑动
      if (deltaX > 0) {
        onSwipeRight?.();
      } else {
        onSwipeLeft?.();
      }
    } else {
      // 垂直滑动
      if (deltaY > 0) {
        onSwipeDown?.();
      } else {
        onSwipeUp?.();
      }
    }

    touchStartRef.current = null;
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, threshold]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 添加触摸事件监听器
    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventDefaultTouchmove });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, preventDefaultTouchmove]);

  return elementRef;
}

// 长按手势Hook
interface LongPressOptions {
  onLongPress: () => void;
  delay?: number;
  shouldPreventDefault?: boolean;
}

export function useLongPress(options: LongPressOptions) {
  const { onLongPress, delay = 500, shouldPreventDefault = true } = options;
  const elementRef = useRef<HTMLElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isLongPressRef = useRef(false);

  const start = useCallback((e: TouchEvent | MouseEvent) => {
    if (shouldPreventDefault) {
      e.preventDefault();
    }

    isLongPressRef.current = false;
    timeoutRef.current = setTimeout(() => {
      isLongPressRef.current = true;
      onLongPress();
    }, delay);
  }, [onLongPress, delay, shouldPreventDefault]);

  const clear = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const clickHandler = useCallback((e: MouseEvent) => {
    if (isLongPressRef.current) {
      e.preventDefault();
      e.stopPropagation();
    }
  }, []);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 鼠标事件
    element.addEventListener('mousedown', start);
    element.addEventListener('mouseup', clear);
    element.addEventListener('mouseleave', clear);
    element.addEventListener('click', clickHandler);

    // 触摸事件
    element.addEventListener('touchstart', start);
    element.addEventListener('touchend', clear);
    element.addEventListener('touchcancel', clear);

    return () => {
      element.removeEventListener('mousedown', start);
      element.removeEventListener('mouseup', clear);
      element.removeEventListener('mouseleave', clear);
      element.removeEventListener('click', clickHandler);
      element.removeEventListener('touchstart', start);
      element.removeEventListener('touchend', clear);
      element.removeEventListener('touchcancel', clear);
      clear();
    };
  }, [start, clear, clickHandler]);

  return elementRef;
}

// 双击手势Hook
interface DoubleTapOptions {
  onDoubleTap: () => void;
  delay?: number;
}

export function useDoubleTap(options: DoubleTapOptions) {
  const { onDoubleTap, delay = 300 } = options;
  const elementRef = useRef<HTMLElement>(null);
  const lastTapRef = useRef<number>(0);

  const handleTap = useCallback(() => {
    const now = Date.now();
    const timeSinceLastTap = now - lastTapRef.current;

    if (timeSinceLastTap < delay && timeSinceLastTap > 0) {
      onDoubleTap();
      lastTapRef.current = 0;
    } else {
      lastTapRef.current = now;
    }
  }, [onDoubleTap, delay]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('click', handleTap);
    element.addEventListener('touchend', handleTap);

    return () => {
      element.removeEventListener('click', handleTap);
      element.removeEventListener('touchend', handleTap);
    };
  }, [handleTap]);

  return elementRef;
}
