/**
 * 医保基金监管系统 - 统一主题颜色管理
 * 
 * 这个文件定义了整个应用的颜色系统，包括：
 * 1. 语义化颜色定义
 * 2. 组件专用颜色
 * 3. 状态颜色
 * 4. 深色模式支持
 */

// 基础颜色系统 - 使用CSS变量，支持主题切换
export const colors = {
  // 主要颜色
  primary: {
    DEFAULT: 'hsl(var(--primary))',
    foreground: 'hsl(var(--primary-foreground))',
    50: 'hsl(var(--primary) / 0.05)',
    100: 'hsl(var(--primary) / 0.1)',
    200: 'hsl(var(--primary) / 0.2)',
    500: 'hsl(var(--primary) / 0.5)',
    600: 'hsl(var(--primary) / 0.6)',
    700: 'hsl(var(--primary) / 0.7)',
  },

  // 次要颜色
  secondary: {
    DEFAULT: 'hsl(var(--secondary))',
    foreground: 'hsl(var(--secondary-foreground))',
  },

  // 背景颜色
  background: {
    DEFAULT: 'hsl(var(--background))',
    secondary: 'hsl(var(--muted))',
  },

  // 前景色（文字）
  foreground: {
    DEFAULT: 'hsl(var(--foreground))',
    muted: 'hsl(var(--muted-foreground))',
  },

  // 边框颜色
  border: {
    DEFAULT: 'hsl(var(--border))',
    muted: 'hsl(var(--border) / 0.5)',
  },

  // 卡片颜色
  card: {
    DEFAULT: 'hsl(var(--card))',
    foreground: 'hsl(var(--card-foreground))',
  },

  // 状态颜色
  status: {
    success: 'hsl(142 76% 36%)', // 绿色
    warning: 'hsl(38 92% 50%)',  // 黄色
    error: 'hsl(var(--destructive))', // 红色
    info: 'hsl(var(--primary))',      // 蓝色
  },

  // 监管系统专用颜色
  supervision: {
    // 违规相关
    violation: {
      high: 'hsl(0 84% 60%)',    // 高风险 - 红色
      medium: 'hsl(38 92% 50%)', // 中风险 - 橙色
      low: 'hsl(43 89% 38%)',    // 低风险 - 黄色
    },
    
    // 审核状态
    audit: {
      pending: 'hsl(43 89% 38%)',   // 待审核 - 黄色
      approved: 'hsl(142 76% 36%)', // 已通过 - 绿色
      rejected: 'hsl(0 84% 60%)',   // 已拒绝 - 红色
    },

    // 基金相关
    fund: {
      normal: 'hsl(142 76% 36%)',   // 正常 - 绿色
      warning: 'hsl(38 92% 50%)',   // 预警 - 橙色
      critical: 'hsl(0 84% 60%)',   // 严重 - 红色
    },
  },
} as const;

// 组件专用颜色类
export const componentColors = {
  // 导航栏
  navbar: {
    background: 'bg-card',
    border: 'border-border',
    text: 'text-foreground',
    logo: 'bg-primary text-primary-foreground',
  },

  // 侧边栏
  sidebar: {
    background: 'bg-card',
    border: 'border-border',
    text: 'text-foreground',
    textMuted: 'text-muted-foreground',
    hover: 'hover:bg-accent hover:text-accent-foreground',
    active: 'bg-primary/10 text-primary border-r-2 border-primary',
  },

  // 按钮
  button: {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  },

  // 徽章
  badge: {
    default: 'bg-secondary text-secondary-foreground',
    destructive: 'bg-destructive text-destructive-foreground',
    warning: 'bg-yellow-500 text-white',
    success: 'bg-green-500 text-white',
  },

  // 状态指示器
  status: {
    success: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    error: 'bg-red-100 text-red-800 border-red-200',
    info: 'bg-blue-100 text-blue-800 border-blue-200',
  },
} as const;

// 主题切换工具函数
export const getThemeColors = (theme: 'light' | 'dark' = 'light') => {
  return theme === 'dark' ? {
    // 深色模式特定颜色
    background: 'hsl(222.2 84% 4.9%)',
    foreground: 'hsl(210 40% 98%)',
    card: 'hsl(222.2 84% 4.9%)',
    // ... 其他深色模式颜色
  } : {
    // 浅色模式颜色
    background: 'hsl(0 0% 100%)',
    foreground: 'hsl(222.2 84% 4.9%)',
    card: 'hsl(0 0% 100%)',
    // ... 其他浅色模式颜色
  };
};

// 导出类型
export type ColorKey = keyof typeof colors;
export type ComponentColorKey = keyof typeof componentColors;
