/**
 * 主题工具函数
 * 提供主题相关的工具函数和类名生成器
 */

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { componentColors } from './colors';

/**
 * 合并类名的工具函数（继承自shadcn/ui）
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 获取组件颜色类名
 */
export function getComponentColors(component: keyof typeof componentColors) {
  return componentColors[component];
}

/**
 * 生成状态相关的类名
 */
export function getStatusClasses(status: 'success' | 'warning' | 'error' | 'info') {
  const statusMap = {
    success: 'bg-green-50 text-green-700 border-green-200',
    warning: 'bg-yellow-50 text-yellow-700 border-yellow-200', 
    error: 'bg-red-50 text-red-700 border-red-200',
    info: 'bg-blue-50 text-blue-700 border-blue-200',
  };
  
  return statusMap[status];
}

/**
 * 生成监管状态相关的类名
 */
export function getSupervisionStatusClasses(
  type: 'violation' | 'audit' | 'fund',
  level: 'high' | 'medium' | 'low' | 'pending' | 'approved' | 'rejected' | 'normal' | 'warning' | 'critical'
) {
  const classMap = {
    violation: {
      high: 'bg-red-50 text-red-700 border-red-200',
      medium: 'bg-orange-50 text-orange-700 border-orange-200',
      low: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    },
    audit: {
      pending: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      approved: 'bg-green-50 text-green-700 border-green-200',
      rejected: 'bg-red-50 text-red-700 border-red-200',
    },
    fund: {
      normal: 'bg-green-50 text-green-700 border-green-200',
      warning: 'bg-orange-50 text-orange-700 border-orange-200',
      critical: 'bg-red-50 text-red-700 border-red-200',
    },
  };

  return classMap[type]?.[level as keyof typeof classMap[typeof type]] || '';
}

/**
 * 生成导航项的类名
 */
export function getNavigationClasses(isActive: boolean, isCollapsed: boolean = false) {
  const baseClasses = 'flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors';
  const activeClasses = componentColors.sidebar.active;
  const inactiveClasses = `${componentColors.sidebar.text} ${componentColors.sidebar.hover}`;
  const collapsedClasses = isCollapsed ? 'justify-center px-2' : '';

  return cn(
    baseClasses,
    isActive ? activeClasses : inactiveClasses,
    collapsedClasses
  );
}

/**
 * 生成徽章的类名
 */
export function getBadgeClasses(variant: 'default' | 'destructive' | 'warning' | 'success' = 'default') {
  const baseClasses = 'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium';
  
  const variantClasses = {
    default: componentColors.badge.default,
    destructive: componentColors.badge.destructive,
    warning: componentColors.badge.warning,
    success: componentColors.badge.success,
  };

  return cn(baseClasses, variantClasses[variant]);
}

/**
 * 生成按钮的类名
 */
export function getButtonClasses(
  variant: 'primary' | 'secondary' | 'ghost' | 'destructive' = 'primary',
  size: 'sm' | 'md' | 'lg' = 'md'
) {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  
  const variantClasses = {
    primary: componentColors.button.primary,
    secondary: componentColors.button.secondary,
    ghost: componentColors.button.ghost,
    destructive: componentColors.button.destructive,
  };

  const sizeClasses = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-10 py-2 px-4',
    lg: 'h-11 px-8',
  };

  return cn(baseClasses, variantClasses[variant], sizeClasses[size]);
}

/**
 * 主题相关的常量
 */
export const THEME_CONSTANTS = {
  // 布局尺寸
  HEADER_HEIGHT: 'h-16',
  SIDEBAR_WIDTH_EXPANDED: 'w-60',
  SIDEBAR_WIDTH_COLLAPSED: 'w-16',
  
  // 动画时长
  TRANSITION_DURATION: 'duration-300',
  
  // 阴影
  SHADOW_SM: 'shadow-sm',
  SHADOW_MD: 'shadow-md',
  SHADOW_LG: 'shadow-lg',
  
  // 圆角
  ROUNDED_SM: 'rounded-sm',
  ROUNDED_MD: 'rounded-md',
  ROUNDED_LG: 'rounded-lg',
} as const;
