// 医疗管理专用类型定义
import { BaseEntity, Patient, Priority, ResultStatus, Attachment } from './index';

// 医疗病例查询和筛选参数
export interface CaseQueryParams {
  page?: number;
  limit?: number;
  search?: string; // 搜索关键词
  caseType?: CaseType[];
  status?: CaseStatus[];
  department?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  priority?: Priority[];
  attendingPhysician?: string;
  sortBy?: CaseSortField;
  sortOrder?: 'asc' | 'desc';
}

export enum CaseSortField {
  CASE_ID = 'caseId',
  PATIENT_NAME = 'patientName',
  ADMISSION_DATE = 'admissionDate',
  DISCHARGE_DATE = 'dischargeDate',
  STATUS = 'status',
  PRIORITY = 'priority',
  DEPARTMENT = 'department',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

// 病例统计数据
export interface CaseStatistics {
  totalCases: number;
  activeCases: number;
  dischargedCases: number;
  emergencyCases: number;
  
  byType: {
    inpatient: number;
    outpatient: number;
    emergency: number;
    daySurgery: number;
  };
  
  byStatus: {
    registered: number;
    inTreatment: number;
    discharged: number;
    transferred: number;
    cancelled: number;
    completed: number;
  };
  
  byDepartment: Array<{
    department: string;
    count: number;
  }>;
  
  averageStayDuration: number; // 平均住院天数
  totalRevenue: number; // 总收入
  
  trends: {
    daily: Array<{
      date: string;
      admissions: number;
      discharges: number;
    }>;
    monthly: Array<{
      month: string;
      cases: number;
      revenue: number;
    }>;
  };
}

// 病例表单数据
export interface CaseFormData {
  // 基本信息
  patientId: string;
  caseType: CaseType;
  priority: Priority;
  department: string;
  ward?: string;
  bedNumber?: string;
  
  // 医疗信息
  chiefComplaint: string;
  presentIllness: string;
  pastHistory?: string;
  familyHistory?: string;
  personalHistory?: string;
  
  // 诊断信息
  primaryDiagnosis: string;
  secondaryDiagnoses: string[];
  differentialDiagnosis?: string;
  
  // 治疗信息
  treatmentPlan: string;
  
  // 医护人员
  attendingPhysician: string;
  residentPhysician?: string;
  nurseInCharge?: string;
  
  // 初始用药
  initialMedications?: Omit<CaseMedication, 'id'>[];
  
  // 备注
  notes?: string;
}

// 病例更新数据
export interface CaseUpdateData extends Partial<CaseFormData> {
  status?: CaseStatus;
  dischargeDate?: Date;
  totalCost?: number;
  insuranceCoverage?: number;
}

// 医疗记录模板
export interface MedicalRecordTemplate {
  id: string;
  name: string;
  recordType: RecordType;
  department: string;
  template: string; // 模板内容，支持变量替换
  variables: TemplateVariable[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateVariable {
  name: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select' | 'textarea';
  required: boolean;
  options?: string[]; // 用于 select 类型
  defaultValue?: string;
}

// 检验项目配置
export interface LabTestConfig {
  id: string;
  testName: string;
  testCode: string;
  category: string;
  sampleType: SampleType;
  parameters: LabParameterConfig[];
  normalProcessingTime: number; // 正常处理时间（小时）
  urgentProcessingTime: number; // 紧急处理时间（小时）
  cost: number;
  isActive: boolean;
}

export interface LabParameterConfig {
  name: string;
  unit?: string;
  referenceRanges: {
    ageGroup?: string;
    gender?: 'male' | 'female';
    min?: number;
    max?: number;
    normalRange: string;
  }[];
  criticalValues?: {
    low?: number;
    high?: number;
  };
}

// 科室配置
export interface Department {
  id: string;
  name: string;
  code: string;
  type: DepartmentType;
  location: string;
  capacity?: number; // 床位数（住院科室）
  head: string; // 科室主任
  contactPhone: string;
  description?: string;
  isActive: boolean;
  
  // 科室统计
  stats?: {
    totalBeds?: number;
    occupiedBeds?: number;
    activeCases: number;
    totalStaff: number;
  };
}

export enum DepartmentType {
  CLINICAL = 'clinical', // 临床科室
  MEDICAL_TECH = 'medical_tech', // 医技科室
  ADMINISTRATIVE = 'administrative', // 行政科室
  SUPPORT = 'support', // 支持科室
}

// 病房管理
export interface Ward {
  id: string;
  name: string;
  departmentId: string;
  department: Department;
  floor: number;
  totalBeds: number;
  availableBeds: number;
  wardType: WardType;
  nurseStation: string;
  isActive: boolean;
  
  beds: Bed[];
}

export interface Bed {
  id: string;
  bedNumber: string;
  wardId: string;
  bedType: BedType;
  status: BedStatus;
  currentPatientId?: string;
  currentCaseId?: string;
  lastCleanedAt?: Date;
  notes?: string;
}

export enum WardType {
  GENERAL = 'general', // 普通病房
  ICU = 'icu', // 重症监护
  CCU = 'ccu', // 冠心病监护
  NICU = 'nicu', // 新生儿重症监护
  ISOLATION = 'isolation', // 隔离病房
  VIP = 'vip', // VIP病房
}

export enum BedType {
  STANDARD = 'standard', // 标准床位
  ELECTRIC = 'electric', // 电动床
  ICU_BED = 'icu_bed', // ICU床位
  ISOLATION_BED = 'isolation_bed', // 隔离床位
}

export enum BedStatus {
  AVAILABLE = 'available', // 可用
  OCCUPIED = 'occupied', // 已占用
  MAINTENANCE = 'maintenance', // 维护中
  CLEANING = 'cleaning', // 清洁中
  RESERVED = 'reserved', // 已预约
}

// 医疗费用
export interface MedicalCost {
  id: string;
  caseId: string;
  itemType: CostItemType;
  itemName: string;
  itemCode?: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  discountAmount?: number;
  finalAmount: number;
  chargeDate: Date;
  chargedBy: string;
  department: string;
  isInsuranceCovered: boolean;
  insuranceRatio?: number;
  notes?: string;
}

export enum CostItemType {
  MEDICATION = 'medication', // 药品费
  EXAMINATION = 'examination', // 检查费
  LABORATORY = 'laboratory', // 化验费
  TREATMENT = 'treatment', // 治疗费
  SURGERY = 'surgery', // 手术费
  NURSING = 'nursing', // 护理费
  BED = 'bed', // 床位费
  MATERIAL = 'material', // 材料费
  OTHER = 'other', // 其他费用
}

// 导入导出相关
export interface CaseExportOptions {
  format: 'excel' | 'pdf' | 'csv';
  fields: string[];
  filters: CaseQueryParams;
  includeAttachments: boolean;
  includeMedicalRecords: boolean;
  includeLabResults: boolean;
}

export interface CaseImportResult {
  totalRecords: number;
  successCount: number;
  failureCount: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
  warnings: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}
