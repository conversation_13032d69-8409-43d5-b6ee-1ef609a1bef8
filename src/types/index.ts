// 基础类型定义
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// 重新导出医疗相关类型
export * from './medical';

// 用户相关类型
export interface User extends BaseEntity {
  username: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  lastLoginAt?: Date;
  profile: UserProfile;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  displayName: string;
  department?: string;
  position?: string;
  bio?: string;
}

export enum UserRole {
  ADMIN = 'admin',
  DOCTOR = 'doctor',
  NURSE = 'nurse',
  TECHNICIAN = 'technician',
  PATIENT = 'patient',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

// 患者相关类型
export interface Patient extends BaseEntity {
  patientId: string; // 患者编号
  name: string;
  gender: Gender;
  dateOfBirth: Date;
  age: number;
  phone?: string;
  email?: string;
  address?: Address;
  emergencyContact?: EmergencyContact;
  medicalHistory: MedicalHistory[];
  allergies: string[];
  medications: Medication[];
  insuranceInfo?: InsuranceInfo;
}

export interface Address {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

export interface MedicalHistory {
  condition: string;
  diagnosedDate: Date;
  status: MedicalHistoryStatus;
  notes?: string;
}

export interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  startDate: Date;
  endDate?: Date;
  prescribedBy: string;
}

export interface InsuranceInfo {
  provider: string;
  policyNumber: string;
  groupNumber?: string;
  expiryDate: Date;
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum MedicalHistoryStatus {
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  CHRONIC = 'chronic',
}

// 检查相关类型
export interface Inspection extends BaseEntity {
  inspectionId: string;
  patientId: string;
  patient: Patient;
  type: InspectionType;
  status: InspectionStatus;
  scheduledDate: Date;
  completedDate?: Date;
  assignedTo: string; // 医生/技师ID
  assignedBy: string; // 分配者ID
  priority: Priority;
  department: string;
  location: string;
  notes?: string;
  results?: InspectionResult[];
  attachments?: Attachment[];
}

export interface InspectionResult {
  id: string;
  parameter: string;
  value: string;
  unit?: string;
  referenceRange?: string;
  status: ResultStatus;
  notes?: string;
}

export interface Attachment {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
}

export enum InspectionType {
  BLOOD_TEST = 'blood_test',
  URINE_TEST = 'urine_test',
  X_RAY = 'x_ray',
  CT_SCAN = 'ct_scan',
  MRI = 'mri',
  ULTRASOUND = 'ultrasound',
  ECG = 'ecg',
  ENDOSCOPY = 'endoscopy',
  BIOPSY = 'biopsy',
  OTHER = 'other',
}

export enum InspectionStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  RESCHEDULED = 'rescheduled',
}

export enum Priority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum ResultStatus {
  NORMAL = 'normal',
  ABNORMAL = 'abnormal',
  CRITICAL = 'critical',
  PENDING = 'pending',
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 表单类型
export interface FormField {
  name: string;
  label: string;
  type: FormFieldType;
  required?: boolean;
  placeholder?: string;
  options?: FormOption[];
  validation?: ValidationRule[];
}

export interface FormOption {
  label: string;
  value: string | number;
}

export interface ValidationRule {
  type: ValidationType;
  value?: any;
  message: string;
}

export enum FormFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  DATE = 'date',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  TEXTAREA = 'textarea',
  FILE = 'file',
}

export enum ValidationType {
  REQUIRED = 'required',
  MIN_LENGTH = 'minLength',
  MAX_LENGTH = 'maxLength',
  PATTERN = 'pattern',
  EMAIL = 'email',
  PHONE = 'phone',
  ID_CARD = 'idCard',
}

// 通知类型
export interface Notification extends BaseEntity {
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  userId: string;
  actionUrl?: string;
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
}

// 医疗管理相关类型
export interface MedicalCase extends BaseEntity {
  caseId: string; // 病例编号
  patientId: string;
  patient: Patient;
  caseType: CaseType;
  status: CaseStatus;
  priority: Priority;

  // 基本信息
  admissionDate: Date; // 入院/就诊日期
  dischargeDate?: Date; // 出院日期
  department: string; // 科室
  ward?: string; // 病房（住院病例）
  bedNumber?: string; // 床位号（住院病例）

  // 医疗信息
  chiefComplaint: string; // 主诉
  presentIllness: string; // 现病史
  pastHistory?: string; // 既往史
  familyHistory?: string; // 家族史
  personalHistory?: string; // 个人史

  // 诊断信息
  primaryDiagnosis: string; // 主要诊断
  secondaryDiagnoses: string[]; // 次要诊断
  differentialDiagnosis?: string; // 鉴别诊断

  // 治疗信息
  treatmentPlan: string; // 治疗方案
  medications: CaseMedication[]; // 用药记录
  procedures: MedicalProcedure[]; // 医疗操作

  // 医护人员
  attendingPhysician: string; // 主治医师
  residentPhysician?: string; // 住院医师
  nurseInCharge?: string; // 责任护士

  // 费用信息
  totalCost?: number; // 总费用
  insuranceCoverage?: number; // 保险覆盖

  // 附件和记录
  medicalRecords: MedicalRecord[]; // 病历记录
  labResults: LabResult[]; // 检验结果
  imagingResults: ImagingResult[]; // 影像结果
  attachments: Attachment[];

  // 状态追踪
  statusHistory: CaseStatusHistory[];
  notes: CaseNote[];
}

export interface CaseMedication {
  id: string;
  medicationName: string;
  dosage: string;
  frequency: string;
  route: MedicationRoute; // 给药途径
  startDate: Date;
  endDate?: Date;
  prescribedBy: string;
  notes?: string;
}

export interface MedicalProcedure {
  id: string;
  procedureName: string;
  procedureCode?: string;
  performedDate: Date;
  performedBy: string;
  assistants?: string[];
  duration?: number; // 持续时间（分钟）
  complications?: string;
  notes?: string;
  cost?: number;
}

export interface MedicalRecord {
  id: string;
  recordType: RecordType;
  recordDate: Date;
  content: string;
  recordedBy: string;
  isTemplate: boolean;
  templateId?: string;
}

export interface LabResult {
  id: string;
  testName: string;
  testCode?: string;
  sampleType: SampleType;
  collectionDate: Date;
  reportDate: Date;
  results: LabTestResult[];
  interpretation?: string;
  performedBy: string;
  reviewedBy?: string;
}

export interface LabTestResult {
  parameter: string;
  value: string;
  unit?: string;
  referenceRange: string;
  status: ResultStatus;
  isAbnormal: boolean;
}

export interface ImagingResult {
  id: string;
  examType: ImagingType;
  examDate: Date;
  bodyPart: string;
  findings: string;
  impression: string;
  radiologist: string;
  images: ImageFile[];
  reportUrl?: string;
}

export interface ImageFile {
  id: string;
  filename: string;
  url: string;
  thumbnailUrl?: string;
  fileSize: number;
  uploadedAt: Date;
}

export interface CaseStatusHistory {
  id: string;
  fromStatus: CaseStatus;
  toStatus: CaseStatus;
  changedAt: Date;
  changedBy: string;
  reason?: string;
  notes?: string;
}

export interface CaseNote {
  id: string;
  noteType: NoteType;
  content: string;
  createdBy: string;
  createdAt: Date;
  isPrivate: boolean;
  tags?: string[];
}

// 枚举类型
export enum CaseType {
  INPATIENT = 'inpatient', // 住院
  OUTPATIENT = 'outpatient', // 门诊
  EMERGENCY = 'emergency', // 急诊
  DAY_SURGERY = 'day_surgery', // 日间手术
}

export enum CaseStatus {
  REGISTERED = 'registered', // 已登记
  IN_TREATMENT = 'in_treatment', // 治疗中
  DISCHARGED = 'discharged', // 已出院
  TRANSFERRED = 'transferred', // 已转科
  CANCELLED = 'cancelled', // 已取消
  COMPLETED = 'completed', // 已完成
}

export enum MedicationRoute {
  ORAL = 'oral', // 口服
  IV = 'intravenous', // 静脉注射
  IM = 'intramuscular', // 肌肉注射
  SC = 'subcutaneous', // 皮下注射
  TOPICAL = 'topical', // 外用
  INHALATION = 'inhalation', // 吸入
  RECTAL = 'rectal', // 直肠给药
}

export enum RecordType {
  ADMISSION = 'admission', // 入院记录
  PROGRESS = 'progress', // 病程记录
  DISCHARGE = 'discharge', // 出院记录
  CONSULTATION = 'consultation', // 会诊记录
  OPERATION = 'operation', // 手术记录
  NURSING = 'nursing', // 护理记录
}

export enum SampleType {
  BLOOD = 'blood', // 血液
  URINE = 'urine', // 尿液
  STOOL = 'stool', // 大便
  SPUTUM = 'sputum', // 痰液
  CSF = 'cerebrospinal_fluid', // 脑脊液
  TISSUE = 'tissue', // 组织
  OTHER = 'other', // 其他
}

export enum ImagingType {
  X_RAY = 'x_ray', // X光
  CT = 'ct_scan', // CT扫描
  MRI = 'mri', // 核磁共振
  ULTRASOUND = 'ultrasound', // 超声
  MAMMOGRAPHY = 'mammography', // 乳腺摄影
  PET = 'pet_scan', // PET扫描
  ENDOSCOPY = 'endoscopy', // 内镜
}

export enum NoteType {
  GENERAL = 'general', // 一般备注
  IMPORTANT = 'important', // 重要提醒
  FOLLOW_UP = 'follow_up', // 随访
  COMPLICATION = 'complication', // 并发症
  ALLERGY = 'allergy', // 过敏反应
}

// 病例查询参数
export interface CaseQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  caseType?: CaseType[];
  status?: CaseStatus[];
  priority?: Priority[];
  department?: string[];
  attendingPhysician?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// 病例表单数据
export interface CaseFormData {
  patientId: string;
  caseType: CaseType;
  priority: Priority;
  department: string;
  ward?: string;
  bedNumber?: string;
  chiefComplaint: string;
  presentIllness: string;
  pastHistory?: string;
  familyHistory?: string;
  personalHistory?: string;
  primaryDiagnosis: string;
  secondaryDiagnoses?: string[];
  differentialDiagnosis?: string;
  treatmentPlan: string;
  attendingPhysician: string;
  residentPhysician?: string;
  nurseInCharge?: string;
}

// 病例更新数据
export interface CaseUpdateData extends Partial<CaseFormData> {
  status?: CaseStatus;
  dischargeDate?: Date;
  dischargeSummary?: string;
  followUpInstructions?: string;
}

// 病例统计数据
export interface CaseStatistics {
  totalCases: number;
  activeCases: number;
  completedCases: number;
  byType: Record<CaseType, number>;
  byStatus: Record<CaseStatus, number>;
  byDepartment: Record<string, number>;
  byPriority: Record<Priority, number>;
  averageStayDuration: number;
  monthlyTrends: Array<{
    month: string;
    count: number;
  }>;
}

// 病例导出选项
export interface CaseExportOptions {
  format: 'excel' | 'pdf' | 'csv';
  fields: string[];
  filters?: CaseQueryParams;
  includeAttachments?: boolean;
  includeMedicalRecords?: boolean;
  includeLabResults?: boolean;
  includeImagingResults?: boolean;
}

// 病例导入结果
export interface CaseImportResult {
  success: boolean;
  totalRecords: number;
  successfulImports: number;
  failedImports: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

// 科室管理
export interface Department extends BaseEntity {
  name: string;
  code: string;
  type: DepartmentType;
  location?: string;
  capacity?: number;
  head?: string;
  contactPhone?: string;
  description?: string;
  isActive: boolean;
  stats?: DepartmentStats;
}

export interface DepartmentStats {
  totalBeds?: number;
  occupiedBeds?: number;
  activeCases: number;
  totalStaff: number;
}

export enum DepartmentType {
  CLINICAL = 'clinical',
  DIAGNOSTIC = 'diagnostic',
  SURGICAL = 'surgical',
  EMERGENCY = 'emergency',
  ADMINISTRATIVE = 'administrative',
}

// 病房管理
export interface Ward extends BaseEntity {
  name: string;
  departmentId: string;
  department: Department;
  floor: number;
  totalBeds: number;
  availableBeds: number;
  wardType: WardType;
  nurseStation?: string;
  isActive: boolean;
  beds: Bed[];
}

export interface Bed extends BaseEntity {
  bedNumber: string;
  wardId: string;
  bedType: BedType;
  status: BedStatus;
  currentPatientId?: string;
  currentCaseId?: string;
  lastCleanedAt?: Date;
  equipment?: string[];
  notes?: string;
}

export enum WardType {
  GENERAL = 'general',
  ICU = 'icu',
  CCU = 'ccu',
  PEDIATRIC = 'pediatric',
  MATERNITY = 'maternity',
  ISOLATION = 'isolation',
}

export enum BedType {
  STANDARD = 'standard',
  ELECTRIC = 'electric',
  ICU = 'icu',
  PEDIATRIC = 'pediatric',
}

export enum BedStatus {
  AVAILABLE = 'available',
  OCCUPIED = 'occupied',
  MAINTENANCE = 'maintenance',
  CLEANING = 'cleaning',
  RESERVED = 'reserved',
}
