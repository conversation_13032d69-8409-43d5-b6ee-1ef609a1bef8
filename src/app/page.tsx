"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useNativeFeatures } from "@/lib/hooks/use-native-features";
import { Activity, BarChart3, Download, FileText, Plus, TrendingUp } from "lucide-react";
import Link from "next/link";
import React from "react";

export default function Home() {
  const [mounted, setMounted] = React.useState(false);
  const { canInstall, installPWA, isInstalled } = useNativeFeatures();

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const handleInstallPWA = async () => {
    const success = await installPWA();
    if (success) {
      console.log('PWA 安装成功');
    }
  };

  if (!mounted) {
    return null; // 避免服务器端渲染问题
  }

  return (
    <div className="space-y-8 p-6">
        {/* 欢迎区域 */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
            MediInspect v3
          </h1>
          <p className="text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
            现代化医疗检查管理系统，支持PWA离线功能，提供完整的患者管理、检查流程和报告生成解决方案。
          </p>


          {/* PWA 安装提示 */}
          {canInstall && !isInstalled && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
              <p className="text-sm text-blue-800 mb-2">
                安装应用到您的设备，享受更好的体验
              </p>
              <Button onClick={handleInstallPWA} size="sm" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                安装应用
              </Button>
            </div>
          )}
        </div>

        {/* 功能卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link href="/dashboard" className="group">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <div className="ml-3">
                  <CardTitle className="text-lg group-hover:text-blue-600">
                    仪表板
                  </CardTitle>
                  <CardDescription>
                    查看系统概览和统计数据
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          </Link>

          <Link href="/medical-cases" className="group">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <FileText className="h-8 w-8 text-green-600" />
                <div className="ml-3">
                  <CardTitle className="text-lg group-hover:text-green-600">
                    医疗管理
                  </CardTitle>
                  <CardDescription>
                    综合医疗信息管理系统
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          </Link>

          <Link href="/inspections" className="group">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <Activity className="h-8 w-8 text-purple-600" />
                <div className="ml-3">
                  <CardTitle className="text-lg group-hover:text-purple-600">
                    检查管理
                  </CardTitle>
                  <CardDescription>
                    安排和管理医疗检查
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          </Link>

          <Link href="/reports" className="group">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <FileText className="h-8 w-8 text-orange-600" />
                <div className="ml-3">
                  <CardTitle className="text-lg group-hover:text-orange-600">
                    报告中心
                  </CardTitle>
                  <CardDescription>
                    生成和查看检查报告
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          </Link>
        </div>

        {/* 快速操作 */}
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>
              常用功能快速入口
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Link href="/patients/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  新增患者
                </Button>
              </Link>
              <Link href="/inspections/new">
                <Button variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  安排检查
                </Button>
              </Link>
              <Link href="/reports/new">
                <Button variant="outline">
                  <FileText className="h-4 w-4 mr-2" />
                  生成报告
                </Button>
              </Link>
              <Link href="/mobile-demo">
                <Button variant="outline">
                  <Activity className="h-4 w-4 mr-2" />
                  移动端演示
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* 系统状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              系统状态
            </CardTitle>
            <CardDescription>
              实时系统数据概览
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-blue-600">0</div>
                <div className="text-sm text-muted-foreground">今日检查</div>
                <Badge variant="secondary" className="text-xs">
                  +0% 较昨日
                </Badge>
              </div>
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-green-600">0</div>
                <div className="text-sm text-muted-foreground">活跃患者</div>
                <Badge variant="secondary" className="text-xs">
                  +0% 较昨日
                </Badge>
              </div>
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-purple-600">0</div>
                <div className="text-sm text-muted-foreground">待处理报告</div>
                <Badge variant="secondary" className="text-xs">
                  +0% 较昨日
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
    </div>
  );
}
