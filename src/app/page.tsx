'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNativeFeatures } from '@/lib/hooks/use-native-features';
import { Activity, BarChart3, FileText, Plus, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

export default function Home() {
  const [mounted, setMounted] = React.useState(false);
  const { canInstall, installPWA, isInstalled } = useNativeFeatures();

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const handleInstallPWA = async () => {
    const success = await installPWA();
    if (success) {
      console.log('PWA 安装成功');
    }
  };

  if (!mounted) {
    return null; // 避免服务器端渲染问题
  }

  return (
    <div className="space-y-8 p-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-gray-900">监管概览</h1>
        <p className="mt-2 text-gray-600">医保基金监管实时数据概览和关键指标监控</p>
      </div>

      {/* 功能卡片 */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Link href="/dashboard" className="group">
          <Card className="cursor-pointer transition-shadow hover:shadow-lg">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <CardTitle className="text-lg group-hover:text-blue-600">仪表板</CardTitle>
                <CardDescription>查看系统概览和统计数据</CardDescription>
              </div>
            </CardHeader>
          </Card>
        </Link>

        <Link href="/medical-cases" className="group">
          <Card className="cursor-pointer transition-shadow hover:shadow-lg">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <FileText className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <CardTitle className="text-lg group-hover:text-green-600">医疗管理</CardTitle>
                <CardDescription>综合医疗信息管理系统</CardDescription>
              </div>
            </CardHeader>
          </Card>
        </Link>

        <Link href="/inspections" className="group">
          <Card className="cursor-pointer transition-shadow hover:shadow-lg">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <Activity className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <CardTitle className="text-lg group-hover:text-purple-600">检查管理</CardTitle>
                <CardDescription>安排和管理医疗检查</CardDescription>
              </div>
            </CardHeader>
          </Card>
        </Link>

        <Link href="/reports" className="group">
          <Card className="cursor-pointer transition-shadow hover:shadow-lg">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <FileText className="h-8 w-8 text-orange-600" />
              <div className="ml-3">
                <CardTitle className="text-lg group-hover:text-orange-600">报告中心</CardTitle>
                <CardDescription>生成和查看检查报告</CardDescription>
              </div>
            </CardHeader>
          </Card>
        </Link>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
          <CardDescription>常用功能快速入口</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Link href="/patients/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新增患者
              </Button>
            </Link>
            <Link href="/inspections/new">
              <Button variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                安排检查
              </Button>
            </Link>
            <Link href="/reports/new">
              <Button variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                生成报告
              </Button>
            </Link>
            <Link href="/mobile-demo">
              <Button variant="outline">
                <Activity className="mr-2 h-4 w-4" />
                移动端演示
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* 系统状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            系统状态
          </CardTitle>
          <CardDescription>实时系统数据概览</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="space-y-2 text-center">
              <div className="text-3xl font-bold text-blue-600">0</div>
              <div className="text-muted-foreground text-sm">今日检查</div>
              <Badge variant="secondary" className="text-xs">
                +0% 较昨日
              </Badge>
            </div>
            <div className="space-y-2 text-center">
              <div className="text-3xl font-bold text-green-600">0</div>
              <div className="text-muted-foreground text-sm">活跃患者</div>
              <Badge variant="secondary" className="text-xs">
                +0% 较昨日
              </Badge>
            </div>
            <div className="space-y-2 text-center">
              <div className="text-3xl font-bold text-purple-600">0</div>
              <div className="text-muted-foreground text-sm">待处理报告</div>
              <Badge variant="secondary" className="text-xs">
                +0% 较昨日
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
