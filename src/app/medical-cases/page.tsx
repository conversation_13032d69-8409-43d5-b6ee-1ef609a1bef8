"use client";

import { CaseDataTable } from "@/components/medical/case-data-table";
import { CaseFilters } from "@/components/medical/case-filters";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { DepartmentApi, MedicalCaseApi } from "@/lib/api/medical-cases";
import { CaseQueryParams, Department, MedicalCase } from "@/types";
import { Activity, Clock, FileDown, Trash2, TrendingUp, Users } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export default function MedicalCasesPage() {
  const router = useRouter();
  
  // 状态管理
  const [cases, setCases] = useState<MedicalCase[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [caseToDelete, setCaseToDelete] = useState<string | null>(null);
  
  // 分页和筛选状态
  const [filters, setFilters] = useState<CaseQueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  // 加载病例数据
  const loadCases = useCallback(async () => {
    try {
      setLoading(true);
      const response = await MedicalCaseApi.getCases(filters);
      setCases(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Failed to load cases:', error);
      toast.error("无法加载病例数据，请稍后重试");
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // 加载科室数据
  const loadDepartments = useCallback(async () => {
    try {
      const depts = await DepartmentApi.getDepartments();
      setDepartments(depts);
    } catch (error) {
      console.error('Failed to load departments:', error);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    loadCases();
    loadDepartments();
  }, [loadCases, loadDepartments]);

  // 处理筛选器变化
  const handleFiltersChange = (newFilters: CaseQueryParams) => {
    setFilters(newFilters);
  };

  // 重置筛选器
  const handleResetFilters = () => {
    setFilters({
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
  };

  // 处理排序
  const handleSort = (field: string) => {
    const newSortOrder = filters.sortBy === field && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    setFilters({
      ...filters,
      sortBy: field as any,
      sortOrder: newSortOrder,
      page: 1
    });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  // 选择病例
  const handleSelectCase = (caseId: string) => {
    setSelectedCases(prev => 
      prev.includes(caseId) 
        ? prev.filter(id => id !== caseId)
        : [...prev, caseId]
    );
  };

  // 全选/取消全选
  const handleSelectAll = (selected: boolean) => {
    setSelectedCases(selected ? cases.map(c => c.id) : []);
  };

  // 查看病例详情
  const handleViewDetails = (caseId: string) => {
    router.push(`/medical-cases/${caseId}`);
  };

  // 编辑病例
  const handleEdit = (caseId: string) => {
    router.push(`/medical-cases/${caseId}/edit`);
  };

  // 删除病例
  const handleDelete = (caseId: string) => {
    setCaseToDelete(caseId);
    setDeleteDialogOpen(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!caseToDelete) return;
    
    try {
      await MedicalCaseApi.deleteCase(caseToDelete);
      toast.success("病例已成功删除");
      loadCases();
      setSelectedCases(prev => prev.filter(id => id !== caseToDelete));
    } catch (error) {
      toast.error("删除病例时发生错误，请稍后重试");
    } finally {
      setDeleteDialogOpen(false);
      setCaseToDelete(null);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedCases.length === 0) return;
    
    try {
      await MedicalCaseApi.deleteCases(selectedCases);
      toast.success(`已成功删除 ${selectedCases.length} 个病例`);
      loadCases();
      setSelectedCases([]);
    } catch (error) {
      toast.error("删除病例时发生错误，请稍后重试");
    }
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const blob = await MedicalCaseApi.exportCases({
        format: 'excel',
        fields: ['caseId', 'patientName', 'caseType', 'status', 'department'],
        filters,
        includeAttachments: false,
        includeMedicalRecords: false,
        includeLabResults: false
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `medical-cases-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success("病例数据已成功导出");
    } catch (error) {
      toast.error("导出数据时发生错误，请稍后重试");
    }
  };

  // 生成分页链接
  const generatePaginationItems = () => {
    const items = [];
    const { page, totalPages } = pagination;
    
    // 显示页码的逻辑
    const showPages = 5;
    let startPage = Math.max(1, page - Math.floor(showPages / 2));
    let endPage = Math.min(totalPages, startPage + showPages - 1);
    
    if (endPage - startPage + 1 < showPages) {
      startPage = Math.max(1, endPage - showPages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={i === page}
            className="cursor-pointer"
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    return items;
  };

  return (
    <div className="space-y-6 p-6">
        {/* 页面标题和统计 */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">医疗管理</h1>
            <p className="text-muted-foreground">
              综合医疗信息管理系统，支持病例、患者、检查等全方位管理
            </p>
          </div>
          
          {/* 统计卡片 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="text-sm font-medium">总病例</div>
                  <div className="text-lg font-bold">{pagination.total}</div>
                </div>
              </div>
            </Card>
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-green-600" />
                <div>
                  <div className="text-sm font-medium">治疗中</div>
                  <div className="text-lg font-bold">
                    {cases.filter(c => c.status === 'in_treatment').length}
                  </div>
                </div>
              </div>
            </Card>
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-orange-600" />
                <div>
                  <div className="text-sm font-medium">今日新增</div>
                  <div className="text-lg font-bold">
                    {cases.filter(c => 
                      new Date(c.createdAt).toDateString() === new Date().toDateString()
                    ).length}
                  </div>
                </div>
              </div>
            </Card>
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-purple-600" />
                <div>
                  <div className="text-sm font-medium">紧急病例</div>
                  <div className="text-lg font-bold">
                    {cases.filter(c => c.priority === 'urgent').length}
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* 筛选器 */}
        <CaseFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onReset={handleResetFilters}
          onExport={handleExport}
          onRefresh={loadCases}
          departments={departments.map(d => ({ id: d.id, name: d.name }))}
          loading={loading}
        />

        {/* 批量操作 */}
        {selectedCases.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">
                    已选择 {selectedCases.length} 个病例
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBatchDelete}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExport}
                  >
                    <FileDown className="h-4 w-4 mr-2" />
                    导出选中
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 数据表格 */}
        <CaseDataTable
          cases={cases}
          loading={loading}
          selectedCases={selectedCases}
          onSelectCase={handleSelectCase}
          onSelectAll={handleSelectAll}
          onSort={handleSort}
          sortField={filters.sortBy}
          sortOrder={filters.sortOrder}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onViewDetails={handleViewDetails}
        />

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              显示第 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
              共 {pagination.total} 条记录
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(pagination.page - 1)}
                    className={pagination.hasPrev ? "cursor-pointer" : "pointer-events-none opacity-50"}
                  />
                </PaginationItem>
                {generatePaginationItems()}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(pagination.page + 1)}
                    className={pagination.hasNext ? "cursor-pointer" : "pointer-events-none opacity-50"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}

        {/* 删除确认对话框 */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认删除</AlertDialogTitle>
              <AlertDialogDescription>
                您确定要删除这个病例吗？此操作无法撤销。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDelete}>
                确认删除
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
  );
}
