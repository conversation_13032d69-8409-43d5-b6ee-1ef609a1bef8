"use client";

import React from "react";
import { ResponsiveLayout } from "@/components/layout/responsive-layout";
import { MobileFeaturesDemo } from "@/components/mobile/mobile-features-demo";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Smartphone, Tablet, Monitor } from "lucide-react";

export default function MobileDemoPage() {
  return (
    <ResponsiveLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold tracking-tight">移动端功能演示</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            体验PWA应用的原生功能集成、触摸手势和移动端优化特性
          </p>
        </div>

        {/* 响应式设计说明 */}
        <Card>
          <CardHeader>
            <CardTitle>响应式设计特性</CardTitle>
            <CardDescription>
              本应用采用移动端优先的响应式设计策略
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50">
                <Smartphone className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="font-medium">移动端</div>
                  <div className="text-sm text-muted-foreground">< 768px</div>
                  <Badge variant="secondary" className="mt-1">触摸优化</Badge>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50">
                <Tablet className="h-8 w-8 text-green-600" />
                <div>
                  <div className="font-medium">平板端</div>
                  <div className="text-sm text-muted-foreground">768px - 1024px</div>
                  <Badge variant="secondary" className="mt-1">自适应布局</Badge>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50">
                <Monitor className="h-8 w-8 text-purple-600" />
                <div>
                  <div className="font-medium">桌面端</div>
                  <div className="text-sm text-muted-foreground">> 1024px</div>
                  <Badge variant="secondary" className="mt-1">完整功能</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 移动端功能演示 */}
        <MobileFeaturesDemo />

        {/* 技术说明 */}
        <Card>
          <CardHeader>
            <CardTitle>技术实现说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">PWA功能</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Service Worker 离线缓存</li>
                <li>• Web App Manifest 应用配置</li>
                <li>• 推送通知 API</li>
                <li>• 应用安装提示</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">原生功能集成</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• MediaDevices API (相机)</li>
                <li>• Geolocation API (定位)</li>
                <li>• Vibration API (震动)</li>
                <li>• Web Share API (分享)</li>
                <li>• File API (文件选择)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">性能优化</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• React Window 虚拟列表</li>
                <li>• 触摸手势识别</li>
                <li>• 响应式图片加载</li>
                <li>• 代码分割和懒加载</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </ResponsiveLayout>
  );
}
