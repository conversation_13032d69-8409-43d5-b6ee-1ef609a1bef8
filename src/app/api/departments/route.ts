// 科室 API 路由处理器

import { NextRequest, NextResponse } from 'next/server';
import { mockDepartments } from '@/lib/api/mock-data';

// GET /api/departments - 获取所有科室
export async function GET(request: NextRequest) {
  try {
    // 模拟从数据库获取科室数据
    const departments = mockDepartments.filter(dept => dept.isActive);

    return NextResponse.json({
      success: true,
      data: departments,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching departments:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch departments',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
