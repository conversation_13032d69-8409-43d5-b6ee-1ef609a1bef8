// 单个医疗病例 API 路由处理器

import { NextRequest, NextResponse } from 'next/server';
import { findCaseById, mockCases } from '@/lib/api/mock-data';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/medical-cases/[id] - 获取单个病例详情
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    
    const medicalCase = findCaseById(id);
    
    if (!medicalCase) {
      return NextResponse.json({
        success: false,
        error: 'Medical case not found',
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: medicalCase,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching medical case:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch medical case',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// PUT /api/medical-cases/[id] - 更新病例
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const updateData = await request.json();
    
    const caseIndex = mockCases.findIndex(c => c.id === id);
    
    if (caseIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'Medical case not found',
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    // 模拟更新病例
    const updatedCase = {
      ...mockCases[caseIndex],
      ...updateData,
      updatedAt: new Date()
    };

    mockCases[caseIndex] = updatedCase;

    return NextResponse.json({
      success: true,
      data: updatedCase,
      message: 'Medical case updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error updating medical case:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update medical case',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// DELETE /api/medical-cases/[id] - 删除病例
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    
    const caseIndex = mockCases.findIndex(c => c.id === id);
    
    if (caseIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'Medical case not found',
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    // 模拟删除病例
    const deletedCase = mockCases.splice(caseIndex, 1)[0];

    return NextResponse.json({
      success: true,
      data: deletedCase,
      message: 'Medical case deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error deleting medical case:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete medical case',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// PATCH /api/medical-cases/[id] - 部分更新病例
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const patchData = await request.json();
    
    const caseIndex = mockCases.findIndex(c => c.id === id);
    
    if (caseIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'Medical case not found',
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    // 模拟部分更新
    const currentCase = mockCases[caseIndex];
    const updatedCase = {
      ...currentCase,
      ...patchData,
      updatedAt: new Date()
    };

    // 如果更新了状态，添加状态历史记录
    if (patchData.status && patchData.status !== currentCase.status) {
      updatedCase.statusHistory = [
        ...currentCase.statusHistory,
        {
          id: `status-${Date.now()}`,
          fromStatus: currentCase.status,
          toStatus: patchData.status,
          changedAt: new Date(),
          changedBy: 'current-user', // 应该从认证信息获取
          reason: patchData.reason || '状态更新',
          notes: patchData.notes
        }
      ];
    }

    mockCases[caseIndex] = updatedCase;

    return NextResponse.json({
      success: true,
      data: updatedCase,
      message: 'Medical case updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error patching medical case:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update medical case',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
