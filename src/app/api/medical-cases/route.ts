// 医疗病例 API 路由处理器

import { mockCases, paginateData } from '@/lib/api/mock-data';
import { CaseStatus, CaseType, Priority } from '@/types';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/medical-cases - 获取病例列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const caseType = searchParams.getAll('caseType') as CaseType[];
    const status = searchParams.getAll('status') as CaseStatus[];
    const department = searchParams.getAll('department');
    const priority = searchParams.getAll('priority') as Priority[];
    const attendingPhysician = searchParams.get('attendingPhysician') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // 过滤数据
    let filteredCases = mockCases.filter(medicalCase => {
      // 搜索过滤
      if (search) {
        const searchLower = search.toLowerCase();
        const matchesSearch = 
          medicalCase.caseId.toLowerCase().includes(searchLower) ||
          medicalCase.patient.name.toLowerCase().includes(searchLower) ||
          medicalCase.primaryDiagnosis.toLowerCase().includes(searchLower) ||
          medicalCase.chiefComplaint.toLowerCase().includes(searchLower);
        
        if (!matchesSearch) return false;
      }

      // 病例类型过滤
      if (caseType.length > 0 && !caseType.includes(medicalCase.caseType)) {
        return false;
      }

      // 状态过滤
      if (status.length > 0 && !status.includes(medicalCase.status)) {
        return false;
      }

      // 科室过滤
      if (department.length > 0 && !department.includes(medicalCase.department)) {
        return false;
      }

      // 优先级过滤
      if (priority.length > 0 && !priority.includes(medicalCase.priority)) {
        return false;
      }

      // 主治医师过滤
      if (attendingPhysician && !medicalCase.attendingPhysician.includes(attendingPhysician)) {
        return false;
      }

      return true;
    });

    // 排序
    filteredCases.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'caseId':
          aValue = a.caseId;
          bValue = b.caseId;
          break;
        case 'patientName':
          aValue = a.patient.name;
          bValue = b.patient.name;
          break;
        case 'admissionDate':
          aValue = new Date(a.admissionDate);
          bValue = new Date(b.admissionDate);
          break;
        case 'dischargeDate':
          aValue = a.dischargeDate ? new Date(a.dischargeDate) : new Date(0);
          bValue = b.dischargeDate ? new Date(b.dischargeDate) : new Date(0);
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'priority':
          aValue = a.priority;
          bValue = b.priority;
          break;
        case 'department':
          aValue = a.department;
          bValue = b.department;
          break;
        case 'updatedAt':
          aValue = new Date(a.updatedAt);
          bValue = new Date(b.updatedAt);
          break;
        default:
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    // 分页
    const result = paginateData(filteredCases, page, limit);

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching medical cases:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch medical cases',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// POST /api/medical-cases - 创建新病例
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 这里应该有数据验证逻辑
    // 模拟创建新病例
    const newCase = {
      id: `case-${Date.now()}`,
      caseId: `CASE${String(mockCases.length + 1).padStart(4, '0')}`,
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
      medicalRecords: [],
      labResults: [],
      imagingResults: [],
      attachments: [],
      statusHistory: [{
        id: `status-${Date.now()}`,
        fromStatus: null,
        toStatus: data.status || CaseStatus.REGISTERED,
        changedAt: new Date(),
        changedBy: 'current-user', // 应该从认证信息获取
        reason: '新建病例'
      }],
      notes: []
    };

    // 模拟保存到数据库
    mockCases.push(newCase as any);

    return NextResponse.json({
      success: true,
      data: newCase,
      message: 'Medical case created successfully',
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating medical case:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create medical case',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// DELETE /api/medical-cases - 批量删除病例
export async function DELETE(request: NextRequest) {
  try {
    const { caseIds } = await request.json();
    
    if (!Array.isArray(caseIds) || caseIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Invalid case IDs provided',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    // 模拟批量删除
    const deletedCount = caseIds.length;
    
    // 在实际应用中，这里应该从数据库中删除
    // 这里只是模拟，不实际删除 mockCases 中的数据

    return NextResponse.json({
      success: true,
      data: { deletedCount },
      message: `Successfully deleted ${deletedCount} medical cases`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error deleting medical cases:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete medical cases',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
