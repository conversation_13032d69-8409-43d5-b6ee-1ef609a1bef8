import { SupervisionLayout } from '@/components/layout/supervision-layout';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import type { Metadata, Viewport } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'MediInspect v3 - 医疗检查管理系统',
  description:
    '现代化医疗检查管理系统，支持PWA离线功能，提供完整的患者管理、检查流程和报告生成解决方案。',
  keywords: ['医疗', '检查', '管理系统', 'PWA', '患者管理', '医院'],
  authors: [{ name: 'MediInspect Team' }],
  creator: 'MediInspect Team',
  publisher: 'MediInspect',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'MediInspect v3',
  },
  openGraph: {
    type: 'website',
    siteName: 'MediInspect v3',
    title: 'MediInspect v3 - 医疗检查管理系统',
    description: '现代化医疗检查管理系统，支持PWA离线功能',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MediInspect v3 - 医疗检查管理系统',
    description: '现代化医疗检查管理系统，支持PWA离线功能',
  },
};

export const viewport: Viewport = {
  themeColor: '#2563eb',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="MediInspect v3" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#2563eb" />
        <meta name="msapplication-tap-highlight" content="no" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
        <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#2563eb" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} bg-background text-foreground antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <SupervisionLayout>{children}</SupervisionLayout>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
