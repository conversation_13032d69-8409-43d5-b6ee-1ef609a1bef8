"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/components/ui/select";
import { CaseQueryParams, CaseStatus, CaseType, Priority } from "@/types";
import {
    Download,
    Filter,
    Plus,
    RefreshCw,
    Search,
    X
} from "lucide-react";

interface CaseFiltersProps {
  filters: CaseQueryParams;
  onFiltersChange: (filters: CaseQueryParams) => void;
  onReset: () => void;
  onExport?: () => void;
  onRefresh?: () => void;
  departments?: Array<{ id: string; name: string }>;
  physicians?: Array<{ id: string; name: string }>;
  loading?: boolean;
}

export function CaseFilters({
  filters,
  onFiltersChange,
  onReset,
  onExport,
  onRefresh,
  departments = [],
  physicians = [],
  loading = false
}: CaseFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, search: value, page: 1 });
  };

  const handleCaseTypeChange = (value: string) => {
    const currentTypes = filters.caseType || [];
    let newTypes: CaseType[];
    
    if (value === 'all') {
      newTypes = [];
    } else {
      const caseType = value as CaseType;
      if (currentTypes.includes(caseType)) {
        newTypes = currentTypes.filter(t => t !== caseType);
      } else {
        newTypes = [...currentTypes, caseType];
      }
    }
    
    onFiltersChange({ ...filters, caseType: newTypes, page: 1 });
  };

  const handleStatusChange = (value: string) => {
    const currentStatuses = filters.status || [];
    let newStatuses: CaseStatus[];
    
    if (value === 'all') {
      newStatuses = [];
    } else {
      const status = value as CaseStatus;
      if (currentStatuses.includes(status)) {
        newStatuses = currentStatuses.filter(s => s !== status);
      } else {
        newStatuses = [...currentStatuses, status];
      }
    }
    
    onFiltersChange({ ...filters, status: newStatuses, page: 1 });
  };

  const handlePriorityChange = (value: string) => {
    const currentPriorities = filters.priority || [];
    let newPriorities: Priority[];
    
    if (value === 'all') {
      newPriorities = [];
    } else {
      const priority = value as Priority;
      if (currentPriorities.includes(priority)) {
        newPriorities = currentPriorities.filter(p => p !== priority);
      } else {
        newPriorities = [...currentPriorities, priority];
      }
    }
    
    onFiltersChange({ ...filters, priority: newPriorities, page: 1 });
  };

  const handleDepartmentChange = (value: string) => {
    const currentDepartments = filters.department || [];
    let newDepartments: string[];
    
    if (value === 'all') {
      newDepartments = [];
    } else {
      if (currentDepartments.includes(value)) {
        newDepartments = currentDepartments.filter(d => d !== value);
      } else {
        newDepartments = [...currentDepartments, value];
      }
    }
    
    onFiltersChange({ ...filters, department: newDepartments, page: 1 });
  };

  const handlePhysicianChange = (value: string) => {
    onFiltersChange({ 
      ...filters, 
      attendingPhysician: value === 'all' ? '' : value, 
      page: 1 
    });
  };

  const removeFilter = (filterType: string, value?: string) => {
    switch (filterType) {
      case 'search':
        onFiltersChange({ ...filters, search: '', page: 1 });
        break;
      case 'caseType':
        if (value) {
          const newTypes = (filters.caseType || []).filter(t => t !== value);
          onFiltersChange({ ...filters, caseType: newTypes, page: 1 });
        }
        break;
      case 'status':
        if (value) {
          const newStatuses = (filters.status || []).filter(s => s !== value);
          onFiltersChange({ ...filters, status: newStatuses, page: 1 });
        }
        break;
      case 'priority':
        if (value) {
          const newPriorities = (filters.priority || []).filter(p => p !== value);
          onFiltersChange({ ...filters, priority: newPriorities, page: 1 });
        }
        break;
      case 'department':
        if (value) {
          const newDepartments = (filters.department || []).filter(d => d !== value);
          onFiltersChange({ ...filters, department: newDepartments, page: 1 });
        }
        break;
      case 'physician':
        onFiltersChange({ ...filters, attendingPhysician: '', page: 1 });
        break;
    }
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.caseType?.length) count += filters.caseType.length;
    if (filters.status?.length) count += filters.status.length;
    if (filters.priority?.length) count += filters.priority.length;
    if (filters.department?.length) count += filters.department.length;
    if (filters.attendingPhysician) count++;
    return count;
  };

  const caseTypeLabels = {
    [CaseType.INPATIENT]: '住院',
    [CaseType.OUTPATIENT]: '门诊',
    [CaseType.EMERGENCY]: '急诊',
    [CaseType.DAY_SURGERY]: '日间手术'
  };

  const statusLabels = {
    [CaseStatus.REGISTERED]: '已登记',
    [CaseStatus.IN_TREATMENT]: '治疗中',
    [CaseStatus.DISCHARGED]: '已出院',
    [CaseStatus.TRANSFERRED]: '已转科',
    [CaseStatus.CANCELLED]: '已取消',
    [CaseStatus.COMPLETED]: '已完成'
  };

  const priorityLabels = {
    [Priority.LOW]: '低优先级',
    [Priority.NORMAL]: '普通',
    [Priority.HIGH]: '高优先级',
    [Priority.URGENT]: '紧急'
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            筛选条件
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
            >
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              新建病例
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索病例编号、患者姓名、诊断..."
            value={filters.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 筛选器 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Select onValueChange={handleCaseTypeChange}>
            <SelectTrigger>
              <SelectValue placeholder="病例类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              {Object.entries(caseTypeLabels).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={handleStatusChange}>
            <SelectTrigger>
              <SelectValue placeholder="病例状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              {Object.entries(statusLabels).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={handlePriorityChange}>
            <SelectTrigger>
              <SelectValue placeholder="优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              {Object.entries(priorityLabels).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={handleDepartmentChange}>
            <SelectTrigger>
              <SelectValue placeholder="科室" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部科室</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept.id} value={dept.name}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={handlePhysicianChange}>
            <SelectTrigger>
              <SelectValue placeholder="主治医师" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部医师</SelectItem>
              {physicians.map((physician) => (
                <SelectItem key={physician.id} value={physician.name}>
                  {physician.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 活动筛选器标签 */}
        {getActiveFiltersCount() > 0 && (
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <Badge variant="secondary" className="gap-1">
                搜索: {filters.search}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('search')}
                />
              </Badge>
            )}
            
            {filters.caseType?.map(type => (
              <Badge key={type} variant="secondary" className="gap-1">
                {caseTypeLabels[type]}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('caseType', type)}
                />
              </Badge>
            ))}
            
            {filters.status?.map(status => (
              <Badge key={status} variant="secondary" className="gap-1">
                {statusLabels[status]}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('status', status)}
                />
              </Badge>
            ))}
            
            {filters.priority?.map(priority => (
              <Badge key={priority} variant="secondary" className="gap-1">
                {priorityLabels[priority]}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('priority', priority)}
                />
              </Badge>
            ))}
            
            {filters.department?.map(dept => (
              <Badge key={dept} variant="secondary" className="gap-1">
                {dept}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('department', dept)}
                />
              </Badge>
            ))}
            
            {filters.attendingPhysician && (
              <Badge variant="secondary" className="gap-1">
                医师: {filters.attendingPhysician}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('physician')}
                />
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onReset}
              className="h-6 px-2 text-xs"
            >
              清除全部
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
