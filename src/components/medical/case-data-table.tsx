"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";
import { CaseStatus, CaseType, MedicalCase, Priority } from "@/types";
import {
    ArrowDown,
    ArrowUp,
    ArrowUpDown,
    Edit,
    Eye,
    FileText,
    MoreHorizontal,
    Trash2
} from "lucide-react";
import Link from "next/link";

interface CaseDataTableProps {
  cases: MedicalCase[];
  loading?: boolean;
  selectedCases: string[];
  onSelectCase: (caseId: string) => void;
  onSelectAll: (selected: boolean) => void;
  onSort: (field: string) => void;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  onEdit?: (caseId: string) => void;
  onDelete?: (caseId: string) => void;
  onViewDetails?: (caseId: string) => void;
}

export function CaseDataTable({
  cases,
  loading = false,
  selectedCases,
  onSelectCase,
  onSelectAll,
  onSort,
  sortField,
  sortOrder,
  onEdit,
  onDelete,
  onViewDetails
}: CaseDataTableProps) {
  const allSelected = cases.length > 0 && selectedCases.length === cases.length;
  const someSelected = selectedCases.length > 0 && selectedCases.length < cases.length;

  const handleSelectAll = (checked: boolean) => {
    onSelectAll(checked);
  };

  const handleSelectCase = (caseId: string, checked: boolean) => {
    onSelectCase(caseId);
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const getStatusBadge = (status: CaseStatus) => {
    const variants = {
      [CaseStatus.REGISTERED]: 'secondary',
      [CaseStatus.IN_TREATMENT]: 'default',
      [CaseStatus.DISCHARGED]: 'outline',
      [CaseStatus.TRANSFERRED]: 'secondary',
      [CaseStatus.CANCELLED]: 'destructive',
      [CaseStatus.COMPLETED]: 'outline'
    } as const;

    const labels = {
      [CaseStatus.REGISTERED]: '已登记',
      [CaseStatus.IN_TREATMENT]: '治疗中',
      [CaseStatus.DISCHARGED]: '已出院',
      [CaseStatus.TRANSFERRED]: '已转科',
      [CaseStatus.CANCELLED]: '已取消',
      [CaseStatus.COMPLETED]: '已完成'
    };

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {labels[status] || status}
      </Badge>
    );
  };

  const getCaseTypeBadge = (type: CaseType) => {
    const labels = {
      [CaseType.INPATIENT]: '住院',
      [CaseType.OUTPATIENT]: '门诊',
      [CaseType.EMERGENCY]: '急诊',
      [CaseType.DAY_SURGERY]: '日间手术'
    };

    return (
      <Badge variant="outline">
        {labels[type] || type}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: Priority) => {
    const variants = {
      [Priority.LOW]: 'secondary',
      [Priority.NORMAL]: 'outline',
      [Priority.HIGH]: 'default',
      [Priority.URGENT]: 'destructive'
    } as const;

    const labels = {
      [Priority.LOW]: '低',
      [Priority.NORMAL]: '普通',
      [Priority.HIGH]: '高',
      [Priority.URGENT]: '紧急'
    };

    return (
      <Badge variant={variants[priority]}>
        {labels[priority]}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={allSelected}
                onCheckedChange={handleSelectAll}
                aria-label="选择全部"
                ref={(ref) => {
                  if (ref) {
                    ref.indeterminate = someSelected;
                  }
                }}
              />
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => onSort('caseId')}
                className="h-auto p-0 font-medium"
              >
                病例编号
                {getSortIcon('caseId')}
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => onSort('patientName')}
                className="h-auto p-0 font-medium"
              >
                患者姓名
                {getSortIcon('patientName')}
              </Button>
            </TableHead>
            <TableHead>类型</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>优先级</TableHead>
            <TableHead>科室</TableHead>
            <TableHead>主治医师</TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => onSort('admissionDate')}
                className="h-auto p-0 font-medium"
              >
                入院日期
                {getSortIcon('admissionDate')}
              </Button>
            </TableHead>
            <TableHead>主要诊断</TableHead>
            <TableHead className="w-12">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {cases.length === 0 ? (
            <TableRow>
              <TableCell colSpan={11} className="text-center py-8 text-muted-foreground">
                暂无病例数据
              </TableCell>
            </TableRow>
          ) : (
            cases.map((medicalCase) => (
              <TableRow key={medicalCase.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedCases.includes(medicalCase.id)}
                    onCheckedChange={(checked) => handleSelectCase(medicalCase.id, checked as boolean)}
                    aria-label={`选择病例 ${medicalCase.caseId}`}
                  />
                </TableCell>
                <TableCell className="font-medium">
                  <Link 
                    href={`/medical-cases/${medicalCase.id}`}
                    className="text-primary hover:underline"
                  >
                    {medicalCase.caseId}
                  </Link>
                </TableCell>
                <TableCell>{medicalCase.patient.name}</TableCell>
                <TableCell>{getCaseTypeBadge(medicalCase.caseType)}</TableCell>
                <TableCell>{getStatusBadge(medicalCase.status)}</TableCell>
                <TableCell>{getPriorityBadge(medicalCase.priority)}</TableCell>
                <TableCell>{medicalCase.department}</TableCell>
                <TableCell>{medicalCase.attendingPhysician}</TableCell>
                <TableCell>{formatDate(medicalCase.admissionDate)}</TableCell>
                <TableCell className="max-w-xs truncate" title={medicalCase.primaryDiagnosis}>
                  {medicalCase.primaryDiagnosis}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">打开菜单</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewDetails?.(medicalCase.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEdit?.(medicalCase.id)}>
                        <Edit className="mr-2 h-4 w-4" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <FileText className="mr-2 h-4 w-4" />
                        生成报告
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => onDelete?.(medicalCase.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
