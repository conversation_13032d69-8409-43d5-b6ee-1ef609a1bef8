"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNativeFeatures } from "@/lib/hooks/use-native-features";
import { useSwipeGesture, useLongPress, useDoubleTap } from "@/lib/hooks/use-swipe-gesture";
import { VirtualList } from "./virtual-list";
import { 
  Camera, 
  MapPin, 
  Bell, 
  Vibrate, 
  Share2, 
  Download,
  Smartphone,
  Wifi,
  WifiOff,
  TouchpadIcon as Touch
} from "lucide-react";

export function MobileFeaturesDemo() {
  const [lastAction, setLastAction] = useState<string>("");
  const [demoData] = useState(Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    name: `患者 ${i + 1}`,
    status: i % 3 === 0 ? "active" : i % 3 === 1 ? "pending" : "completed"
  })));

  const {
    isInstalled,
    canInstall,
    installPWA,
    capturePhoto,
    getCurrentLocation,
    requestNotificationPermission,
    sendNotification,
    vibrate,
    shareContent,
    selectFile,
    isOnline,
    deviceInfo
  } = useNativeFeatures();

  // 手势演示
  const swipeRef = useSwipeGesture({
    onSwipeLeft: () => setLastAction("向左滑动"),
    onSwipeRight: () => setLastAction("向右滑动"),
    onSwipeUp: () => setLastAction("向上滑动"),
    onSwipeDown: () => setLastAction("向下滑动"),
  });

  const longPressRef = useLongPress({
    onLongPress: () => setLastAction("长按触发"),
    delay: 500
  });

  const doubleTapRef = useDoubleTap({
    onDoubleTap: () => setLastAction("双击触发")
  });

  // 原生功能演示
  const handleCameraCapture = async () => {
    try {
      const stream = await capturePhoto();
      setLastAction("相机启动成功");
      // 这里可以处理拍照逻辑
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      setLastAction("相机启动失败");
    }
  };

  const handleLocationRequest = async () => {
    try {
      const position = await getCurrentLocation();
      setLastAction(`位置获取成功: ${position.coords.latitude}, ${position.coords.longitude}`);
    } catch (error) {
      setLastAction("位置获取失败");
    }
  };

  const handleNotification = async () => {
    try {
      const hasPermission = await requestNotificationPermission();
      if (hasPermission) {
        await sendNotification("测试通知", {
          body: "这是一个测试通知消息",
          tag: "test-notification"
        });
        setLastAction("通知发送成功");
      } else {
        setLastAction("通知权限被拒绝");
      }
    } catch (error) {
      setLastAction("通知发送失败");
    }
  };

  const handleVibration = () => {
    const success = vibrate([200, 100, 200]);
    setLastAction(success ? "设备震动成功" : "设备不支持震动");
  };

  const handleShare = async () => {
    const success = await shareContent({
      title: "MediInspect v3",
      text: "现代化医疗检查管理系统",
      url: window.location.href
    });
    setLastAction(success ? "分享成功" : "分享失败或取消");
  };

  const handleFileSelect = async () => {
    const files = await selectFile("image/*", false);
    setLastAction(files ? `选择了文件: ${files[0]?.name}` : "文件选择取消");
  };

  const handleInstallPWA = async () => {
    const success = await installPWA();
    setLastAction(success ? "PWA安装成功" : "PWA安装失败");
  };

  const renderVirtualListItem = ({ index, style, data }: any) => {
    const item = data[index];
    return (
      <div style={style} className="flex items-center justify-between p-4 border-b">
        <div>
          <div className="font-medium">{item.name}</div>
          <div className="text-sm text-muted-foreground">ID: {item.id}</div>
        </div>
        <Badge variant={
          item.status === "active" ? "default" : 
          item.status === "pending" ? "secondary" : 
          "outline"
        }>
          {item.status}
        </Badge>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 设备信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            设备信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center gap-2">
            {isOnline ? <Wifi className="h-4 w-4 text-green-600" /> : <WifiOff className="h-4 w-4 text-red-600" />}
            <span className="text-sm">{isOnline ? "在线" : "离线"}</span>
          </div>
          <div className="text-sm space-y-1">
            <div>设备类型: {deviceInfo.isMobile ? "移动设备" : deviceInfo.isTablet ? "平板设备" : "桌面设备"}</div>
            <div>操作系统: {deviceInfo.isIOS ? "iOS" : deviceInfo.isAndroid ? "Android" : "其他"}</div>
            <div>PWA状态: {isInstalled ? "已安装" : canInstall ? "可安装" : "不支持"}</div>
          </div>
        </CardContent>
      </Card>

      {/* PWA功能 */}
      {canInstall && !isInstalled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              PWA安装
            </CardTitle>
            <CardDescription>
              将应用安装到设备主屏幕
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleInstallPWA} className="w-full">
              <Download className="h-4 w-4 mr-2" />
              安装应用
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 原生功能演示 */}
      <Card>
        <CardHeader>
          <CardTitle>原生功能集成</CardTitle>
          <CardDescription>
            测试设备原生功能
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" onClick={handleCameraCapture} className="h-auto py-3">
              <Camera className="h-4 w-4 mr-2" />
              相机
            </Button>
            <Button variant="outline" onClick={handleLocationRequest} className="h-auto py-3">
              <MapPin className="h-4 w-4 mr-2" />
              定位
            </Button>
            <Button variant="outline" onClick={handleNotification} className="h-auto py-3">
              <Bell className="h-4 w-4 mr-2" />
              通知
            </Button>
            <Button variant="outline" onClick={handleVibration} className="h-auto py-3">
              <Vibrate className="h-4 w-4 mr-2" />
              震动
            </Button>
            <Button variant="outline" onClick={handleShare} className="h-auto py-3">
              <Share2 className="h-4 w-4 mr-2" />
              分享
            </Button>
            <Button variant="outline" onClick={handleFileSelect} className="h-auto py-3">
              <Download className="h-4 w-4 mr-2" />
              文件
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 手势演示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Touch className="h-5 w-5" />
            触摸手势演示
          </CardTitle>
          <CardDescription>
            在下方区域尝试滑动、长按、双击
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            ref={(el) => {
              if (el) {
                swipeRef.current = el;
                longPressRef.current = el;
                doubleTapRef.current = el;
              }
            }}
            className="h-32 bg-muted rounded-lg flex items-center justify-center border-2 border-dashed border-muted-foreground/25"
          >
            <div className="text-center">
              <div className="text-sm text-muted-foreground mb-2">触摸手势测试区域</div>
              {lastAction && (
                <Badge variant="secondary">{lastAction}</Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 虚拟列表演示 */}
      <Card>
        <CardHeader>
          <CardTitle>虚拟列表演示</CardTitle>
          <CardDescription>
            高性能渲染1000条数据
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VirtualList
            items={demoData}
            height={300}
            itemHeight={73}
            renderItem={renderVirtualListItem}
            className="border rounded-lg"
          />
        </CardContent>
      </Card>
    </div>
  );
}
