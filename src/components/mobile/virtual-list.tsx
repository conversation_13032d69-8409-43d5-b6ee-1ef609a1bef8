"use client";

import React, { memo, forwardRef } from 'react';
import { FixedSizeList as List, VariableSizeList } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import { cn } from '@/lib/utils';

// 固定高度虚拟列表
interface VirtualListProps {
  items: any[];
  height: number;
  itemHeight: number;
  renderItem: ({ index, style, data }: any) => React.ReactNode;
  className?: string;
  onLoadMore?: () => Promise<void>;
  hasNextPage?: boolean;
  isNextPageLoading?: boolean;
}

export const VirtualList = memo(forwardRef<any, VirtualListProps>(({
  items,
  height,
  itemHeight,
  renderItem,
  className,
  onLoadMore,
  hasNextPage = false,
  isNextPageLoading = false
}, ref) => {
  // 如果支持无限加载
  if (onLoadMore) {
    const itemCount = hasNextPage ? items.length + 1 : items.length;
    const isItemLoaded = (index: number) => !!items[index];

    const loadMoreItems = isNextPageLoading ? () => {} : onLoadMore;

    return (
      <div className={cn("virtual-list-container", className)}>
        <InfiniteLoader
          isItemLoaded={isItemLoaded}
          itemCount={itemCount}
          loadMoreItems={loadMoreItems}
        >
          {({ onItemsRendered, ref: infiniteRef }) => (
            <List
              ref={(list) => {
                infiniteRef(list);
                if (ref) {
                  if (typeof ref === 'function') {
                    ref(list);
                  } else {
                    ref.current = list;
                  }
                }
              }}
              height={height}
              itemCount={itemCount}
              itemSize={itemHeight}
              itemData={items}
              onItemsRendered={onItemsRendered}
              className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
            >
              {({ index, style, data }) => {
                if (index >= items.length) {
                  return (
                    <div style={style} className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  );
                }
                return renderItem({ index, style, data });
              }}
            </List>
          )}
        </InfiniteLoader>
      </div>
    );
  }

  // 普通虚拟列表
  return (
    <div className={cn("virtual-list-container", className)}>
      <List
        ref={ref}
        height={height}
        itemCount={items.length}
        itemSize={itemHeight}
        itemData={items}
        className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        {renderItem}
      </List>
    </div>
  );
}));

VirtualList.displayName = 'VirtualList';

// 可变高度虚拟列表
interface VariableVirtualListProps {
  items: any[];
  height: number;
  getItemHeight: (index: number) => number;
  renderItem: ({ index, style, data }: any) => React.ReactNode;
  className?: string;
  estimatedItemSize?: number;
}

export const VariableVirtualList = memo(forwardRef<any, VariableVirtualListProps>(({
  items,
  height,
  getItemHeight,
  renderItem,
  className,
  estimatedItemSize = 50
}, ref) => {
  return (
    <div className={cn("variable-virtual-list-container", className)}>
      <VariableSizeList
        ref={ref}
        height={height}
        itemCount={items.length}
        itemSize={getItemHeight}
        itemData={items}
        estimatedItemSize={estimatedItemSize}
        className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        {renderItem}
      </VariableSizeList>
    </div>
  );
}));

VariableVirtualList.displayName = 'VariableVirtualList';

// 网格虚拟列表
interface VirtualGridProps {
  items: any[];
  height: number;
  itemHeight: number;
  itemWidth: number;
  columnCount: number;
  renderItem: ({ columnIndex, rowIndex, style, data }: any) => React.ReactNode;
  className?: string;
}

export const VirtualGrid = memo(forwardRef<any, VirtualGridProps>(({
  items,
  height,
  itemHeight,
  itemWidth,
  columnCount,
  renderItem,
  className
}, ref) => {
  const rowCount = Math.ceil(items.length / columnCount);

  return (
    <div className={cn("virtual-grid-container", className)}>
      <List
        ref={ref}
        height={height}
        itemCount={rowCount}
        itemSize={itemHeight}
        itemData={{ items, columnCount, itemWidth }}
        className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        {({ index: rowIndex, style, data }) => (
          <div style={style} className="flex">
            {Array.from({ length: columnCount }, (_, columnIndex) => {
              const itemIndex = rowIndex * columnCount + columnIndex;
              if (itemIndex >= items.length) return null;
              
              return (
                <div
                  key={columnIndex}
                  style={{ width: itemWidth }}
                  className="flex-shrink-0"
                >
                  {renderItem({
                    columnIndex,
                    rowIndex,
                    style: { width: itemWidth, height: itemHeight },
                    data: items[itemIndex]
                  })}
                </div>
              );
            })}
          </div>
        )}
      </List>
    </div>
  );
}));

VirtualGrid.displayName = 'VirtualGrid';

// 响应式虚拟列表Hook
export function useResponsiveVirtualList() {
  const [dimensions, setDimensions] = React.useState({
    width: 0,
    height: 0,
    itemHeight: 60,
    columnCount: 1
  });

  React.useEffect(() => {
    const updateDimensions = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // 根据屏幕宽度调整列数和项目高度
      let columnCount = 1;
      let itemHeight = 60;
      
      if (width >= 1024) {
        columnCount = 4;
        itemHeight = 80;
      } else if (width >= 768) {
        columnCount = 3;
        itemHeight = 70;
      } else if (width >= 640) {
        columnCount = 2;
        itemHeight = 65;
      }

      setDimensions({
        width,
        height,
        itemHeight,
        columnCount
      });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  return dimensions;
}
