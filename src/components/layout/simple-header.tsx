"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Bell, 
  User, 
  Plus,
  FileText
} from "lucide-react";

export function SimpleHeader() {
  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 z-50">
      {/* 左侧区域 */}
      <div className="flex items-center space-x-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">MI</span>
          </div>
          <span className="font-semibold text-lg text-gray-900 hidden sm:block">
            MediInspect
          </span>
        </Link>
        
        {/* 面包屑导航 */}
        <div className="hidden md:block">
          <span className="text-sm text-gray-600">医保基金监管系统</span>
        </div>
      </div>
      
      {/* 中间区域 - 搜索 */}
      <div className="flex items-center space-x-4 flex-1 max-w-2xl mx-8">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索医疗记录、案例、规则..."
            className="pl-10 pr-4"
          />
        </div>
        
        {/* 快速操作按钮 */}
        <div className="hidden lg:flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            新建记录
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            生成报告
          </Button>
        </div>
      </div>
      
      {/* 右侧区域 */}
      <div className="flex items-center space-x-3">
        {/* 通知 */}
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            3
          </Badge>
        </Button>
        
        {/* 用户 */}
        <Button variant="ghost" size="sm" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="h-4 w-4" />
          </div>
          <span className="hidden md:block text-sm font-medium">张监管员</span>
        </Button>
      </div>
    </header>
  );
}
