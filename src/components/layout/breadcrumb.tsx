"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

// 路由映射配置
const routeMap: Record<string, BreadcrumbItem[]> = {
  "/": [
    { label: "监管概览", href: "/", icon: <Home className="h-4 w-4" /> }
  ],
  "/medical-records": [
    { label: "监管概览", href: "/" },
    { label: "医疗记录", href: "/medical-records" }
  ],
  "/medical-records/import": [
    { label: "监管概览", href: "/" },
    { label: "医疗记录", href: "/medical-records" },
    { label: "数据导入" }
  ],
  "/fund-supervision": [
    { label: "监管概览", href: "/" },
    { label: "基金监管", href: "/fund-supervision" }
  ],
  "/fund-supervision/rules": [
    { label: "监管概览", href: "/" },
    { label: "基金监管", href: "/fund-supervision" },
    { label: "规则管理" }
  ],
  "/fund-supervision/violations": [
    { label: "监管概览", href: "/" },
    { label: "基金监管", href: "/fund-supervision" },
    { label: "违规筛选" }
  ],
  "/fund-supervision/audit": [
    { label: "监管概览", href: "/" },
    { label: "基金监管", href: "/fund-supervision" },
    { label: "专家审核" }
  ],
  "/fund-supervision/reports": [
    { label: "监管概览", href: "/" },
    { label: "基金监管", href: "/fund-supervision" },
    { label: "检查报告" }
  ],
  "/knowledge-base": [
    { label: "监管概览", href: "/" },
    { label: "知识库", href: "/knowledge-base" }
  ],
  "/knowledge-base/regulations": [
    { label: "监管概览", href: "/" },
    { label: "知识库", href: "/knowledge-base" },
    { label: "政策法规" }
  ],
  "/knowledge-base/cases": [
    { label: "监管概览", href: "/" },
    { label: "知识库", href: "/knowledge-base" },
    { label: "案例库" }
  ],
  "/knowledge-base/ai-chat": [
    { label: "监管概览", href: "/" },
    { label: "知识库", href: "/knowledge-base" },
    { label: "AI问答" }
  ],
  "/analytics": [
    { label: "监管概览", href: "/" },
    { label: "数据分析", href: "/analytics" }
  ],
  "/analytics/statistics": [
    { label: "监管概览", href: "/" },
    { label: "数据分析", href: "/analytics" },
    { label: "统计分析" }
  ],
  "/analytics/reports": [
    { label: "监管概览", href: "/" },
    { label: "数据分析", href: "/analytics" },
    { label: "可视化报表" }
  ],
  "/analytics/predictions": [
    { label: "监管概览", href: "/" },
    { label: "数据分析", href: "/analytics" },
    { label: "趋势预测" }
  ],
  "/settings": [
    { label: "监管概览", href: "/" },
    { label: "系统设置", href: "/settings" }
  ]
};

export function Breadcrumb() {
  const pathname = usePathname();
  
  // 获取当前路径的面包屑
  const breadcrumbItems = routeMap[pathname] || [
    { label: "监管概览", href: "/" },
    { label: "未知页面" }
  ];

  return (
    <nav className="flex items-center space-x-1 text-sm text-gray-600">
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-gray-400" />
          )}
          
          {item.href ? (
            <Link
              href={item.href}
              className={cn(
                "flex items-center space-x-1 hover:text-gray-900 transition-colors",
                index === breadcrumbItems.length - 1 && "text-gray-900 font-medium"
              )}
            >
              {item.icon}
              <span>{item.label}</span>
            </Link>
          ) : (
            <span className="flex items-center space-x-1 text-gray-900 font-medium">
              {item.icon}
              <span>{item.label}</span>
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}
