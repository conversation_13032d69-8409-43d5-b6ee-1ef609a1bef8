"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface ContextPanelProps {
  children: React.ReactNode;
  open: boolean;
  onClose: () => void;
  title?: string;
  width?: number;
}

export function ContextPanel({ 
  children, 
  open, 
  onClose, 
  title = "详细信息",
  width = 320 
}: ContextPanelProps) {
  return (
    <div
      className={cn(
        "fixed right-0 top-16 h-[calc(100vh-4rem)] bg-white border-l border-gray-200 shadow-lg transition-transform duration-300 z-30",
        open ? "translate-x-0" : "translate-x-full"
      )}
      style={{ width: `${width}px` }}
    >
      <div className="flex flex-col h-full">
        {/* 面板头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900">{title}</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* 面板内容 */}
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
}
