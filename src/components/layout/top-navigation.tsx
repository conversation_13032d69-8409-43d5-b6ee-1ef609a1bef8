"use client";

import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Menu, 
  Search, 
  Bell, 
  User, 
  Settings, 
  LogOut,
  PanelRight,
  Plus,
  FileText,
  AlertTriangle
} from "lucide-react";
import { Breadcrumb } from "./breadcrumb";

interface TopNavigationProps {
  onToggleSidebar: () => void;
  onToggleContextPanel?: () => void;
  showContextPanelToggle?: boolean;
}

export function TopNavigation({ 
  onToggleSidebar, 
  onToggleContextPanel,
  showContextPanelToggle = false 
}: TopNavigationProps) {
  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 z-50">
      {/* 左侧区域 */}
      <div className="flex items-center space-x-4">
        {/* 侧边栏切换按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleSidebar}
          className="lg:hidden"
        >
          <Menu className="h-5 w-5" />
        </Button>
        
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">MI</span>
          </div>
          <span className="font-semibold text-lg text-gray-900 hidden sm:block">
            MediInspect
          </span>
        </Link>
        
        {/* 面包屑导航 */}
        <div className="hidden md:block">
          <Breadcrumb />
        </div>
      </div>
      
      {/* 中间区域 - 搜索和快速操作 */}
      <div className="flex items-center space-x-4 flex-1 max-w-2xl mx-8">
        {/* 全局搜索 */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索医疗记录、案例、规则..."
            className="pl-10 pr-4"
          />
        </div>
        
        {/* 快速操作按钮 */}
        <div className="hidden lg:flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            新建记录
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            生成报告
          </Button>
        </div>
      </div>
      
      {/* 右侧区域 */}
      <div className="flex items-center space-x-3">
        {/* 通知中心 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-5 w-5" />
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
              >
                3
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>通知中心</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="space-y-2 p-2">
              <div className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded-md">
                <AlertTriangle className="h-4 w-4 text-red-500 mt-1" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">发现高风险违规案例</p>
                  <p className="text-xs text-gray-500">涉及金额超过10万元，需要紧急审核</p>
                  <p className="text-xs text-gray-400">5分钟前</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded-md">
                <FileText className="h-4 w-4 text-blue-500 mt-1" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">月度检查报告已生成</p>
                  <p className="text-xs text-gray-500">2024年1月监管报告可供下载</p>
                  <p className="text-xs text-gray-400">1小时前</p>
                </div>
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-center">
              查看全部通知
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        {/* 上下文面板切换 */}
        {showContextPanelToggle && onToggleContextPanel && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleContextPanel}
          >
            <PanelRight className="h-5 w-5" />
          </Button>
        )}
        
        {/* 用户菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="h-4 w-4" />
              </div>
              <span className="hidden md:block text-sm font-medium">张监管员</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>我的账户</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              个人资料
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              系统设置
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600">
              <LogOut className="mr-2 h-4 w-4" />
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
