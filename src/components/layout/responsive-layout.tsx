"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Bell, Menu, Search, User } from "lucide-react";
import React from "react";

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function ResponsiveLayout({ children, className }: ResponsiveLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className={cn(
      "min-h-screen bg-background",
      "md:container md:mx-auto",
      "lg:max-w-7xl lg:px-8",
      className
    )}>
      {/* 响应式导航栏 */}
      <nav className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4 md:px-6">
          {/* 移动端汉堡菜单 */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">打开菜单</span>
          </Button>

          {/* Logo */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-sm font-bold">M</span>
            </div>
            <span className="hidden font-bold sm:inline-block">
              MediInspect v3
            </span>
          </div>

          {/* 桌面端导航 */}
          <div className="hidden md:flex md:items-center md:space-x-6 md:ml-6">
            <a
              href="/dashboard"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              仪表板
            </a>
            <a
              href="/medical-cases"
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
            >
              医疗管理
            </a>
            <a
              href="/reports"
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
            >
              报告中心
            </a>
          </div>

          {/* 右侧工具栏 */}
          <div className="ml-auto flex items-center space-x-2">
            {/* 搜索按钮 */}
            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <Search className="h-4 w-4" />
              <span className="sr-only">搜索</span>
            </Button>

            {/* 通知按钮 */}
            <Button variant="ghost" size="icon">
              <Bell className="h-4 w-4" />
              <span className="sr-only">通知</span>
            </Button>

            {/* 用户菜单 */}
            <Button variant="ghost" size="icon">
              <User className="h-4 w-4" />
              <span className="sr-only">用户菜单</span>
            </Button>
          </div>
        </div>
      </nav>

      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 移动端侧边栏 */}
      <div
        className={cn(
          "fixed left-0 top-14 z-50 h-[calc(100vh-3.5rem)] w-64 transform bg-background border-r transition-transform duration-200 ease-in-out md:hidden",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col space-y-2 p-4">
          <a
            href="/dashboard"
            className="flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent"
          >
            <span>仪表板</span>
          </a>
          <a
            href="/medical-cases"
            className="flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent"
          >
            <span>医疗管理</span>
          </a>
          <a
            href="/reports"
            className="flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent"
          >
            <span>报告中心</span>
          </a>
        </div>
      </div>

      {/* 主内容区域 */}
      <main className="flex-1 p-4 md:p-6 lg:p-8">
        {children}
      </main>
    </div>
  );
}
