"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  Dashboard,
  FileText,
  Shield,
  BookOpen,
  BarChart3,
  Settings
} from "lucide-react";

const navigationItems = [
  {
    id: "dashboard",
    label: "监管概览",
    icon: Dashboard,
    href: "/"
  },
  {
    id: "medical-records",
    label: "医疗记录",
    icon: FileText,
    href: "/medical-records"
  },
  {
    id: "fund-supervision",
    label: "基金监管",
    icon: Shield,
    href: "/fund-supervision"
  },
  {
    id: "knowledge-base",
    label: "知识库",
    icon: BookOpen,
    href: "/knowledge-base"
  },
  {
    id: "analytics",
    label: "数据分析",
    icon: BarChart3,
    href: "/analytics"
  }
];

export function SimpleNavigation() {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  return (
    <aside className="w-60 bg-white border-r border-gray-200">
      <nav className="p-4">
        <div className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            
            return (
              <Link
                key={item.id}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  active
                    ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700"
                    : "text-gray-700 hover:bg-gray-100"
                )}
              >
                <Icon className="h-5 w-5" />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </div>
        
        {/* 底部设置 */}
        <div className="mt-8 pt-4 border-t border-gray-200">
          <Link
            href="/settings"
            className={cn(
              "flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
              isActive("/settings")
                ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700"
                : "text-gray-700 hover:bg-gray-100"
            )}
          >
            <Settings className="h-5 w-5" />
            <span>系统设置</span>
          </Link>
        </div>
      </nav>
    </aside>
  );
}
