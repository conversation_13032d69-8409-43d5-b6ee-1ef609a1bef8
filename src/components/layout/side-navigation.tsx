"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Dashboard,
  FileText,
  Shield,
  BookOpen,
  BarChart3,
  Settings,
  ChevronDown,
  ChevronRight,
  Database,
  Upload,
  CheckCircle,
  AlertTriangle,
  Users,
  ClipboardList,
  TrendingUp,
  PieChart,
  Target,
  UserCog,
  Sliders,
  Activity
} from "lucide-react";

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
  badge?: string | number;
  children?: NavigationItem[];
}

// 导航配置
const navigationItems: NavigationItem[] = [
  {
    id: "dashboard",
    label: "监管概览",
    icon: <Dashboard className="h-5 w-5" />,
    href: "/",
    badge: "实时"
  },
  {
    id: "medical-records",
    label: "医疗记录",
    icon: <FileText className="h-5 w-5" />,
    href: "/medical-records",
    children: [
      {
        id: "records-list",
        label: "记录管理",
        icon: <Database className="h-4 w-4" />,
        href: "/medical-records"
      },
      {
        id: "records-import",
        label: "数据导入",
        icon: <Upload className="h-4 w-4" />,
        href: "/medical-records/import"
      },
      {
        id: "records-quality",
        label: "质量检查",
        icon: <CheckCircle className="h-4 w-4" />,
        href: "/medical-records/quality"
      }
    ]
  },
  {
    id: "fund-supervision",
    label: "基金监管",
    icon: <Shield className="h-5 w-5" />,
    href: "/fund-supervision",
    badge: 12,
    children: [
      {
        id: "rules-management",
        label: "规则管理",
        icon: <Sliders className="h-4 w-4" />,
        href: "/fund-supervision/rules"
      },
      {
        id: "violation-screening",
        label: "违规筛选",
        icon: <AlertTriangle className="h-4 w-4" />,
        href: "/fund-supervision/violations",
        badge: 8
      },
      {
        id: "expert-audit",
        label: "专家审核",
        icon: <Users className="h-4 w-4" />,
        href: "/fund-supervision/audit",
        badge: 4
      },
      {
        id: "inspection-reports",
        label: "检查报告",
        icon: <ClipboardList className="h-4 w-4" />,
        href: "/fund-supervision/reports"
      }
    ]
  },
  {
    id: "knowledge-base",
    label: "知识库",
    icon: <BookOpen className="h-5 w-5" />,
    href: "/knowledge-base",
    children: [
      {
        id: "regulations",
        label: "政策法规",
        icon: <FileText className="h-4 w-4" />,
        href: "/knowledge-base/regulations"
      },
      {
        id: "case-library",
        label: "案例库",
        icon: <Database className="h-4 w-4" />,
        href: "/knowledge-base/cases"
      },
      {
        id: "ai-chat",
        label: "AI问答",
        icon: <Activity className="h-4 w-4" />,
        href: "/knowledge-base/ai-chat"
      }
    ]
  },
  {
    id: "analytics",
    label: "数据分析",
    icon: <BarChart3 className="h-5 w-5" />,
    href: "/analytics",
    children: [
      {
        id: "statistics",
        label: "统计分析",
        icon: <PieChart className="h-4 w-4" />,
        href: "/analytics/statistics"
      },
      {
        id: "visual-reports",
        label: "可视化报表",
        icon: <BarChart3 className="h-4 w-4" />,
        href: "/analytics/reports"
      },
      {
        id: "trend-prediction",
        label: "趋势预测",
        icon: <TrendingUp className="h-4 w-4" />,
        href: "/analytics/predictions"
      }
    ]
  }
];

interface SideNavigationProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

export function SideNavigation({ collapsed, onCollapse }: SideNavigationProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>(["fund-supervision"]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = item.href ? isActive(item.href) : false;

    if (hasChildren) {
      return (
        <Collapsible
          key={item.id}
          open={isExpanded}
          onOpenChange={() => toggleExpanded(item.id)}
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start h-10 px-3",
                level > 0 && "ml-4 w-auto",
                active && "bg-blue-50 text-blue-700 border-r-2 border-blue-700",
                collapsed && "justify-center px-2"
              )}
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center space-x-3">
                  {item.icon}
                  {!collapsed && (
                    <>
                      <span className="font-medium">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </div>
                {!collapsed && (
                  isExpanded ? 
                    <ChevronDown className="h-4 w-4" /> : 
                    <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </Button>
          </CollapsibleTrigger>
          {!collapsed && (
            <CollapsibleContent className="space-y-1">
              <div className="ml-4 space-y-1">
                {item.children?.map(child => renderNavigationItem(child, level + 1))}
              </div>
            </CollapsibleContent>
          )}
        </Collapsible>
      );
    }

    return (
      <Link key={item.id} href={item.href || "#"}>
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start h-10 px-3",
            level > 0 && "ml-4 w-auto",
            active && "bg-blue-50 text-blue-700 border-r-2 border-blue-700",
            collapsed && "justify-center px-2"
          )}
        >
          <div className="flex items-center space-x-3">
            {item.icon}
            {!collapsed && (
              <>
                <span className="font-medium">{item.label}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </>
            )}
          </div>
        </Button>
      </Link>
    );
  };

  return (
    <aside 
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white border-r border-gray-200 transition-all duration-300 z-40",
        collapsed ? "w-16" : "w-60"
      )}
    >
      <div className="flex flex-col h-full">
        {/* 导航内容 */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navigationItems.map(item => renderNavigationItem(item))}
        </nav>
        
        {/* 底部设置 */}
        <div className="p-4 border-t border-gray-200">
          <Link href="/settings">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start h-10 px-3",
                isActive("/settings") && "bg-blue-50 text-blue-700 border-r-2 border-blue-700",
                collapsed && "justify-center px-2"
              )}
            >
              <div className="flex items-center space-x-3">
                <Settings className="h-5 w-5" />
                {!collapsed && <span className="font-medium">系统设置</span>}
              </div>
            </Button>
          </Link>
        </div>
        
        {/* 折叠按钮 */}
        <div className="p-4 border-t border-gray-200">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCollapse(!collapsed)}
            className={cn(
              "w-full",
              collapsed && "justify-center"
            )}
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <>
                <ChevronRight className="h-4 w-4 mr-2 rotate-180" />
                <span>收起</span>
              </>
            )}
          </Button>
        </div>
      </div>
    </aside>
  );
}
