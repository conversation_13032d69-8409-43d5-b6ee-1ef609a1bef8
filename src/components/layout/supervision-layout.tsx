'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  BarChart3,
  Bell,
  BookOpen,
  FileText,
  Home,
  Menu,
  Settings,
  Shield,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';

export function SupervisionLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // 键盘快捷键支持
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + B 切换侧边栏
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        setSidebarCollapsed(!sidebarCollapsed);
      }
      // ESC 关闭移动端菜单
      if (event.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [sidebarCollapsed, mobileMenuOpen]);

  // 导航项配置（带图标和徽章）
  const navigationItems = [
    {
      id: 'dashboard',
      label: '监管概览',
      href: '/',
      icon: Home,
      badge: null,
    },
    {
      id: 'medical-records',
      label: '医疗记录',
      href: '/medical-records',
      icon: FileText,
      badge: null,
    },
    {
      id: 'fund-supervision',
      label: '基金监管',
      href: '/fund-supervision',
      icon: Shield,
      badge: 12, // 待审核数量
    },
    {
      id: 'knowledge-base',
      label: '知识库',
      href: '/knowledge-base',
      icon: BookOpen,
      badge: null,
    },
    {
      id: 'analytics',
      label: '数据分析',
      href: '/analytics',
      icon: BarChart3,
      badge: null,
    },
  ];

  const isActive = (href: string) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  return (
    <div className="flex h-screen flex-col bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="z-50 flex h-16 items-center justify-between border-b border-gray-200 bg-white px-4">
        {/* 左侧区域 */}
        <div className="flex items-center space-x-4">
          {/* 菜单切换按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // 移动端控制菜单开关，桌面端控制侧边栏折叠
              if (window.innerWidth < 1024) {
                setMobileMenuOpen(!mobileMenuOpen);
              } else {
                setSidebarCollapsed(!sidebarCollapsed);
              }
            }}
            title="菜单"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Logo和系统标题 */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
              <span className="text-sm font-bold text-white">医</span>
            </div>
            <span className="text-lg font-semibold text-gray-900">医保基金监管系统</span>
          </Link>
        </div>

        {/* 中间区域 - 预留空间 */}
        <div className="flex-1"></div>

        {/* 右侧区域 */}
        <div className="flex items-center space-x-2 sm:space-x-3">
          {/* 通知中心 */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full p-0 text-xs sm:h-5 sm:w-5"
            >
              3
            </Badge>
          </Button>

          {/* 用户菜单 */}
          <Button variant="ghost" size="sm" className="flex items-center space-x-2">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 sm:h-8 sm:w-8">
              <User className="h-3 w-3 sm:h-4 sm:w-4" />
            </div>
            <span className="hidden text-sm font-medium md:block">张监管员</span>
          </Button>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        {/* 移动端遮罩层 */}
        {mobileMenuOpen && (
          <div
            className="bg-opacity-50 fixed inset-0 z-40 bg-black lg:hidden"
            onClick={() => setMobileMenuOpen(false)}
          />
        )}

        {/* 侧边导航栏 */}
        <aside
          className={cn(
            'z-50 border-r border-gray-200 bg-white transition-all duration-300',
            // 桌面端 (lg+)
            'hidden lg:block',
            sidebarCollapsed ? 'lg:w-16' : 'lg:w-60',
            // 平板端 (md-lg)
            'md:hidden',
            // 移动端 (sm-)
            'fixed top-16 left-0 h-[calc(100vh-4rem)] lg:relative',
            mobileMenuOpen ? 'block w-60' : 'hidden lg:block'
          )}
        >
          <nav className="p-4">
            <div className="space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const active = isActive(item.href);

                return (
                  <Link
                    key={item.id}
                    href={item.href}
                    className={cn(
                      'flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                      active
                        ? 'border-r-2 border-blue-700 bg-blue-50 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100',
                      sidebarCollapsed && 'justify-center px-2'
                    )}
                  >
                    <Icon className="h-5 w-5 flex-shrink-0" />
                    {!sidebarCollapsed && (
                      <>
                        <span className="flex-1">{item.label}</span>
                        {item.badge && (
                          <Badge variant="secondary" className="ml-auto">
                            {item.badge}
                          </Badge>
                        )}
                      </>
                    )}
                  </Link>
                );
              })}
            </div>

            {/* 底部设置 */}
            <div className="mt-8 border-t border-gray-200 pt-4">
              <Link
                href="/settings"
                className={cn(
                  'flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                  isActive('/settings')
                    ? 'border-r-2 border-blue-700 bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100',
                  sidebarCollapsed && 'justify-center px-2'
                )}
              >
                <Settings className="h-5 w-5 flex-shrink-0" />
                {!sidebarCollapsed && <span>系统设置</span>}
              </Link>
            </div>

            {/* 折叠按钮 */}
            <div className="mt-4 border-t border-gray-200 pt-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className={cn('w-full', sidebarCollapsed && 'justify-center')}
              >
                {sidebarCollapsed ? (
                  <Menu className="h-4 w-4" />
                ) : (
                  <>
                    <Menu className="mr-2 h-4 w-4 rotate-180" />
                    <span>收起</span>
                  </>
                )}
              </Button>
            </div>
          </nav>
        </aside>

        {/* 主工作区域 */}
        <main className="flex-1 overflow-auto bg-white">
          <div className="h-full p-4 sm:p-6">{children}</div>
        </main>

        {/* 上下文面板 - 暂时禁用 */}
      </div>
    </div>
  );
}
