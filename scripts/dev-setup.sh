#!/bin/bash

# MediInspect v3 开发环境设置脚本

echo "🏥 MediInspect v3 开发环境设置"
echo "================================"

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node_version=$(node -v)
echo "当前 Node.js 版本: $node_version"

# 检查 npm 版本
echo "📋 检查 npm 版本..."
npm_version=$(npm -v)
echo "当前 npm 版本: $npm_version"

# 安装依赖
echo "📦 安装项目依赖..."
npm install

# 设置 Git hooks
echo "🔧 设置 Git hooks..."
npx husky install

# 运行类型检查
echo "🔍 运行 TypeScript 类型检查..."
npm run type-check

# 运行代码检查
echo "🔍 运行 ESLint 检查..."
npm run lint

# 运行代码格式化检查
echo "🎨 检查代码格式..."
npm run format:check

echo "✅ 开发环境设置完成！"
echo ""
echo "🚀 可用的开发命令："
echo "  npm run dev          - 启动开发服务器"
echo "  npm run build        - 构建生产版本"
echo "  npm run lint         - 运行 ESLint 检查"
echo "  npm run lint:fix     - 自动修复 ESLint 问题"
echo "  npm run format       - 格式化代码"
echo "  npm run type-check   - TypeScript 类型检查"
echo ""
echo "📱 PWA 功能："
echo "  - 支持离线访问"
echo "  - 可安装到设备主屏幕"
echo "  - 推送通知支持"
echo "  - 原生功能集成"
echo ""
echo "🎯 访问应用："
echo "  开发环境: http://localhost:3000"
echo "  移动端演示: http://localhost:3000/mobile-demo"
