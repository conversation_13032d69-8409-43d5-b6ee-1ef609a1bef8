{"name": "mediinspect-v3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky install", "test": "echo \"No tests yet\" && exit 0", "test:watch": "echo \"No tests yet\" && exit 0", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.3", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "workbox-webpack-plugin": "^7.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/minimatch": "^6.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "eslint": "^9", "eslint-config-next": "15.4.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check"}}}